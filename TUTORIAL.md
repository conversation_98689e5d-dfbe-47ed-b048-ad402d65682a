# TestTicket Toy – Local Development & Git Workflow Guide

Welcome to **TestTicket Toy**, an open-source ticketing platform inspired by ticketpie.com.  
This guide shows you how to:

1. Run the full stack locally (database, API, and web app).  
2. Work with Git and push changes to GitHub.

---

## 1 Prerequisites

| Tool | Version (minimum) | Purpose |
|------|-------------------|---------|
| Node.js | 16 .x LTS | Builds & runs the API / web |
| npm  | 8 .x | Package manager (yarn works too) |
| Docker & Docker Compose | 20 .x / Compose v2 | Spins up Postgres & optional Redis |
| Git  | latest | Source-control |

Check versions:

```bash
node -v
npm -v
docker compose version
git --version
```

---

## 2 Clone the Repository

```bash
git clone https://github.com/Chaouz/test3.git
cd test3
```

---

## 3 Environment Variables

1. Copy the template:

```bash
cp .env.example .env
```

2. Open `.env` and **at minimum** review / change:

```
POSTGRES_USER=testticket
POSTGRES_PASSWORD=testticketpass
JWT_SECRET=change_me
STRIPE_SECRET_KEY=sk_test_...
```

*(leave others as-is for local testing)*

---

## 4 Install Dependencies (Monorepo Workspaces)

```bash
npm install          # installs root tools + sets up workspaces
```

Under the hood this runs:

```bash
cd backend && npm install
cd ../frontend && npm install
```

---

## 5 Spin Up Infrastructure

### Option A – Single command (recommended)

```bash
docker compose up -d        # db + redis + api + web
```

*Containers build on first run (≈ 2 min).*

### Option B – Manual database only

```bash
docker compose up -d postgres redis
```

Then in **two terminals**:

```bash
# API
cd backend
npm run dev

# Web
cd frontend
npm run dev
```

---

## 6 Database Schema & Seed

```bash
cd backend

# Generate Prisma client
npx prisma generate

# Apply latest migrations
npx prisma migrate dev

# (Optional) Seed demo data
npm run seed
```

Database is reachable on `postgres://localhost:5432/testticketdb`  
Adminer UI: `http://localhost:8080` (if you kept the service).

---

## 7 Available Scripts

| Location | Command | Description |
|----------|---------|-------------|
| root | `npm run dev` | Dev-mode API + web concurrently |
| backend | `npm run dev` | Nodemon + ts-node hot reload |
| backend | `npm test` | Jest tests |
| frontend | `npm run dev` | Vite dev server (port 3000) |
| root | `docker compose down` | Stop containers |

---

## 8 Accessing the App

| Service | URL | Credentials |
|---------|-----|-------------|
| Frontend | http://localhost:3000 | — |
| API | http://localhost:4000/api/v1 | — |
| Postgres | localhost:5432 | `testticket` / `testticketpass` |

---

## 9 Git Workflow

### Initial setup

```bash
git config --global user.name  "Your Name"
git config --global user.email "<EMAIL>"
```

### Feature branch flow

```bash
# 1. Pull latest main
git checkout main
git pull origin main

# 2. Create a branch
git checkout -b feat/my-awesome-change

# 3. Work… then stage
git add .

# 4. Commit logically
git commit -m "feat: add event detail page"

# 5. Push branch
git push -u origin feat/my-awesome-change
```

### Open a Pull Request

1. Visit `https://github.com/Chaouz/test3`.
2. Click **Compare & pull request**.
3. Describe your change, link issues, hit **Create PR**.

### Keeping in sync

```bash
git checkout main
git pull origin main

# Rebase your branch (preferred) or merge
git checkout feat/my-awesome-change
git rebase main
```

Resolve conflicts if prompted, then:

```bash
git push --force-with-lease
```

---

## 10 Troubleshooting

| Problem | Quick Fix |
|---------|-----------|
| `ECONNREFUSED localhost:5432` | `docker compose ps` – is Postgres healthy? |
| API 404 on `/api/*` | Frontend env var `VITE_API_URL` wrong – default is `http://localhost:4000`. |
| `prisma migrate dev` hangs | Containerised Postgres not ready – wait for healthcheck. |
| Node modules issues | `rm -rf node_modules && npm ci` |
| Merge conflicts | `git status` → edit files → `git add` → `git rebase --continue` |

---

## 11 Next Steps

* Generate real Stripe keys in `.env`
* Deploy using the Docker images or Vercel (frontend) + Render (backend)
* Enable HTTPS & secure cookies in production

Happy hacking! 🎟️
