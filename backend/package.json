{"name": "testticket-toy-backend", "version": "0.1.0", "description": "Backend API for TestTicket Toy ticketing platform", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "ts-node src/database/seed.ts", "migrate:dev": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "docker:build": "docker build -t testticket-backend .", "prepare": "npm run prisma:generate"}, "dependencies": {"@prisma/client": "^5.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.9.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "pdfkit": "^0.13.0", "qrcode": "^1.5.3", "redis": "^4.6.7", "stripe": "^12.17.0", "winston": "^3.10.0", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.4.8", "@types/nodemailer": "^6.4.9", "@types/pdfkit": "^0.12.10", "@types/qrcode": "^1.5.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.46.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.2", "prettier": "^3.0.1", "prisma": "^5.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "prisma": {"schema": "src/database/schema.prisma"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/database/seed.ts"]}}