import { PrismaClient, UserRole, OrganizerTier, VerificationStatus, EventStatus, Visibility, TicketStatus, TransactionStatus, PaymentMethod, DiscountType, CampaignType, CampaignStatus, NotificationType, SupportTicketStatus, TicketPriority } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { faker } from '@faker-js/faker';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Number of records to generate
const NUM_CUSTOMERS = 20;
const NUM_ORGANIZERS = 5;
const NUM_EVENTS = 15;
const NUM_TICKETS_PER_TYPE = 50;
const NUM_TRANSACTIONS = 30;
const NUM_PROMO_CODES = 10;
const NUM_BLOG_POSTS = 8;
const NUM_FAQ_CATEGORIES = 5;
const NUM_FAQ_ITEMS_PER_CATEGORY = 4;
const NUM_SUPPORT_TICKETS = 10;

// Fixed password for all seeded users for easy testing
const DEFAULT_PASSWORD = 'Password123!';

/**
 * Main seed function
 */
async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Hash the default password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, salt);

    // Clear existing data
    await clearDatabase();

    // Seed categories first (needed for events)
    console.log('🔖 Creating event categories...');
    const categories = await seedCategories();

    // Seed users
    console.log('👤 Creating users...');
    const customers = await seedCustomers(hashedPassword);
    const organizers = await seedOrganizers(hashedPassword);
    const admin = await seedAdmin(hashedPassword);

    // Seed events and related data
    console.log('🎭 Creating events...');
    const events = await seedEvents(organizers, categories);

    // Seed ticket types
    console.log('🎟️ Creating ticket types...');
    const ticketTypes = await seedTicketTypes(events);

    // Seed tickets
    console.log('🎫 Creating tickets...');
    const tickets = await seedTickets(ticketTypes);

    // Seed promo codes
    console.log('🏷️ Creating promo codes...');
    await seedPromoCodes(events, organizers);

    // Seed transactions
    console.log('💰 Creating transactions...');
    await seedTransactions(customers, tickets);

    // Seed analytics
    console.log('📊 Creating event analytics...');
    await seedEventAnalytics(events);

    // Seed marketing campaigns
    console.log('📣 Creating marketing campaigns...');
    await seedMarketingCampaigns(events);

    // Seed saved events
    console.log('❤️ Creating saved events...');
    await seedSavedEvents(customers, events);

    // Seed reviews
    console.log('⭐ Creating reviews...');
    await seedReviews(customers, events);

    // Seed notifications
    console.log('🔔 Creating notifications...');
    await seedNotifications(customers.concat(organizers));

    // Seed blog posts
    console.log('📝 Creating blog posts...');
    await seedBlogPosts(admin.id);

    // Seed FAQ content
    console.log('❓ Creating FAQ content...');
    await seedFAQContent();

    // Seed support tickets
    console.log('🎫 Creating support tickets...');
    await seedSupportTickets(customers);

    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * Clear all existing data from the database
 */
async function clearDatabase() {
  console.log('🧹 Clearing existing data...');
  
  // Delete in order to respect foreign key constraints
  await prisma.supportResponse.deleteMany({});
  await prisma.supportTicket.deleteMany({});
  await prisma.faqItem.deleteMany({});
  await prisma.faqCategory.deleteMany({});
  await prisma.blogPost.deleteMany({});
  await prisma.notification.deleteMany({});
  await prisma.review.deleteMany({});
  await prisma.savedEvent.deleteMany({});
  await prisma.marketingCampaign.deleteMany({});
  await prisma.eventAnalytics.deleteMany({});
  await prisma.transaction.deleteMany({});
  await prisma.ticket.deleteMany({});
  await prisma.ticketType.deleteMany({});
  await prisma.promoCode.deleteMany({});
  await prisma.event.deleteMany({});
  await prisma.category.deleteMany({});
  await prisma.teamMember.deleteMany({});
  await prisma.organizerProfile.deleteMany({});
  await prisma.user.deleteMany({});
}

/**
 * Seed customer users
 */
async function seedCustomers(hashedPassword: string) {
  const customers = [];

  for (let i = 0; i < NUM_CUSTOMERS; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    
    const customer = await prisma.user.create({
      data: {
        email: faker.internet.email({ firstName, lastName }).toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phoneNumber: faker.phone.number(),
        profileImage: faker.image.avatar(),
        role: UserRole.CUSTOMER,
        isEmailVerified: faker.datatype.boolean(0.9), // 90% are verified
      },
    });
    
    customers.push(customer);
  }

  return customers;
}

/**
 * Seed organizer users with profiles
 */
async function seedOrganizers(hashedPassword: string) {
  const organizers = [];
  const tierOptions = [OrganizerTier.STARTER, OrganizerTier.GROWTH, OrganizerTier.PROFESSIONAL, OrganizerTier.ENTERPRISE];
  const verificationOptions = [VerificationStatus.PENDING, VerificationStatus.VERIFIED, VerificationStatus.REJECTED];

  for (let i = 0; i < NUM_ORGANIZERS; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const companyName = faker.company.name();
    
    const organizer = await prisma.user.create({
      data: {
        email: faker.internet.email({ firstName, lastName }).toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phoneNumber: faker.phone.number(),
        profileImage: faker.image.avatar(),
        role: UserRole.ORGANIZER,
        isEmailVerified: true,
        organizerProfile: {
          create: {
            companyName,
            companyLogo: faker.image.urlLoremFlickr({ category: 'business' }),
            description: faker.company.catchPhrase(),
            website: faker.internet.url(),
            socialLinks: {
              facebook: `https://facebook.com/${companyName.toLowerCase().replace(/\s/g, '')}`,
              twitter: `https://twitter.com/${companyName.toLowerCase().replace(/\s/g, '')}`,
              instagram: `https://instagram.com/${companyName.toLowerCase().replace(/\s/g, '')}`,
            },
            tier: faker.helpers.arrayElement(tierOptions),
            ticketsSold: faker.number.int({ min: 0, max: 5000 }),
            totalRevenue: faker.number.float({ min: 0, max: 100000, precision: 0.01 }),
            stripeAccountId: faker.string.alphanumeric(20),
            verificationStatus: faker.helpers.arrayElement(verificationOptions),
          },
        },
      },
      include: {
        organizerProfile: true,
      },
    });
    
    organizers.push(organizer);
  }

  return organizers;
}

/**
 * Seed admin user
 */
async function seedAdmin(hashedPassword: string) {
  return prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      phoneNumber: faker.phone.number(),
      profileImage: faker.image.avatar(),
      role: UserRole.ADMIN,
      isEmailVerified: true,
    },
  });
}

/**
 * Seed event categories
 */
async function seedCategories() {
  const categoryData = [
    { name: 'Music', icon: '🎵', color: '#3b82f6' },
    { name: 'Sports', icon: '⚽', color: '#22c55e' },
    { name: 'Arts', icon: '🎨', color: '#f59e0b' },
    { name: 'Food & Drink', icon: '🍷', color: '#ef4444' },
    { name: 'Business', icon: '💼', color: '#6366f1' },
    { name: 'Technology', icon: '💻', color: '#8b5cf6' },
    { name: 'Workshops', icon: '🔧', color: '#64748b' },
    { name: 'Festivals', icon: '🎪', color: '#ec4899' },
  ];

  const categories = [];

  for (const category of categoryData) {
    const slug = category.name.toLowerCase().replace(/\s+/g, '-');
    
    const createdCategory = await prisma.category.create({
      data: {
        name: category.name,
        slug,
        description: `Events related to ${category.name.toLowerCase()}`,
        icon: category.icon,
        color: category.color,
      },
    });
    
    categories.push(createdCategory);
  }

  return categories;
}

/**
 * Seed events
 */
async function seedEvents(organizers: any[], categories: any[]) {
  const events = [];
  const statusOptions = [EventStatus.DRAFT, EventStatus.PUBLISHED, EventStatus.CANCELLED, EventStatus.COMPLETED];
  const visibilityOptions = [Visibility.PUBLIC, Visibility.PRIVATE, Visibility.UNLISTED];
  const currencyOptions = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
  const countries = ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Spain', 'Italy'];
  
  for (let i = 0; i < NUM_EVENTS; i++) {
    const title = faker.helpers.arrayElement([
      `${faker.word.adjective()} ${faker.music.genre()} Festival`,
      `${faker.company.name()} Conference`,
      `${faker.word.adjective()} Food & Wine Expo`,
      `${faker.person.lastName()} Art Exhibition`,
      `${faker.location.city()} Marathon`,
      `${faker.company.name()} Tech Summit`,
      `${faker.word.adjective()} Comedy Show`,
      `${faker.music.songName()} Concert`
    ]);
    
    const slug = title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
    const startDate = faker.date.future();
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + faker.number.int({ min: 1, max: 5 }));
    
    const isOnline = faker.datatype.boolean(0.3); // 30% are online events
    const country = faker.helpers.arrayElement(countries);
    const organizer = faker.helpers.arrayElement(organizers);
    const category = faker.helpers.arrayElement(categories);
    const status = faker.helpers.arrayElement(statusOptions);
    const currency = faker.helpers.arrayElement(currencyOptions);
    
    const event = await prisma.event.create({
      data: {
        title,
        slug,
        description: faker.lorem.paragraphs(3),
        shortDescription: faker.lorem.sentence(),
        bannerImage: faker.image.urlLoremFlickr({ category: 'event' }),
        galleryImages: Array(faker.number.int({ min: 0, max: 5 }))
          .fill(null)
          .map(() => faker.image.urlLoremFlickr({ category: 'event' })),
        startDate,
        endDate,
        timezone: faker.location.timeZone(),
        isOnline,
        venue: isOnline ? undefined : faker.company.name(),
        address: isOnline ? undefined : faker.location.streetAddress(),
        city: isOnline ? undefined : faker.location.city(),
        state: isOnline ? undefined : faker.location.state(),
        country: isOnline ? undefined : country,
        postalCode: isOnline ? undefined : faker.location.zipCode(),
        latitude: isOnline ? undefined : parseFloat(faker.location.latitude()),
        longitude: isOnline ? undefined : parseFloat(faker.location.longitude()),
        currency,
        organizerId: organizer.id,
        categoryId: category.id,
        status,
        visibility: faker.helpers.arrayElement(visibilityOptions),
        maxTicketsPerUser: faker.number.int({ min: 1, max: 10 }),
        termsAndConditions: faker.lorem.paragraphs(2),
      },
    });
    
    events.push(event);
  }

  return events;
}

/**
 * Seed ticket types
 */
async function seedTicketTypes(events: any[]) {
  const ticketTypes = [];
  
  for (const event of events) {
    // Each event has 1-4 ticket types
    const numTicketTypes = faker.number.int({ min: 1, max: 4 });
    
    for (let i = 0; i < numTicketTypes; i++) {
      let name = 'General Admission';
      let isVIP = false;
      let isEarlyBird = false;
      
      // Determine ticket type
      if (i === 0 && faker.datatype.boolean(0.7)) {
        name = 'Early Bird';
        isEarlyBird = true;
      } else if (i === numTicketTypes - 1 && faker.datatype.boolean(0.6)) {
        name = 'VIP';
        isVIP = true;
      } else if (i > 0 && i < numTicketTypes - 1) {
        name = faker.helpers.arrayElement(['Premium', 'Standard', 'Regular', 'Basic']);
      }
      
      // Sales dates
      const salesStartDate = new Date(event.startDate);
      salesStartDate.setDate(salesStartDate.getDate() - faker.number.int({ min: 30, max: 90 }));
      
      const salesEndDate = new Date(event.startDate);
      salesEndDate.setDate(salesEndDate.getDate() - faker.number.int({ min: 1, max: 7 }));
      
      // Price based on ticket type
      let price = faker.number.float({ min: 20, max: 100, precision: 0.01 });
      if (isEarlyBird) {
        price = faker.number.float({ min: 10, max: 50, precision: 0.01 });
      } else if (isVIP) {
        price = faker.number.float({ min: 100, max: 500, precision: 0.01 });
      }
      
      // Quantity and sold
      const quantity = faker.number.int({ min: 50, max: 1000 });
      const quantitySold = faker.number.int({ min: 0, max: quantity * 0.8 });
      
      const ticketType = await prisma.ticketType.create({
        data: {
          name,
          description: faker.lorem.sentence(),
          price,
          currency: event.currency,
          quantity,
          quantitySold,
          eventId: event.id,
          salesStartDate,
          salesEndDate,
          isEarlyBird,
          isVIP,
          maxPerUser: faker.number.int({ min: 1, max: 10 }),
          includesAdditionalItems: isVIP ? {
            drinks: 'Free drinks',
            merchandise: 'Event T-shirt',
            meetAndGreet: true
          } : null,
        },
      });
      
      ticketTypes.push(ticketType);
    }
  }
  
  return ticketTypes;
}

/**
 * Seed tickets
 */
async function seedTickets(ticketTypes: any[]) {
  const tickets = [];
  
  for (const ticketType of ticketTypes) {
    // Create tickets for each ticket type
    const numTickets = Math.min(ticketType.quantitySold + 20, NUM_TICKETS_PER_TYPE);
    
    for (let i = 0; i < numTickets; i++) {
      const ticketNumber = `TKT-${faker.string.alphanumeric(8).toUpperCase()}`;
      const isSold = i < ticketType.quantitySold;
      
      const ticket = await prisma.ticket.create({
        data: {
          ticketNumber,
          ticketTypeId: ticketType.id,
          eventId: ticketType.eventId,
          status: isSold ? TicketStatus.SOLD : TicketStatus.AVAILABLE,
          qrCode: isSold ? `https://api.qrserver.com/v1/create-qr-code/?data=${ticketNumber}&size=200x200` : null,
        },
      });
      
      tickets.push(ticket);
    }
  }
  
  return tickets;
}

/**
 * Seed promo codes
 */
async function seedPromoCodes(events: any[], organizers: any[]) {
  const discountTypes = [DiscountType.PERCENTAGE, DiscountType.FIXED_AMOUNT];
  
  for (let i = 0; i < NUM_PROMO_CODES; i++) {
    const event = faker.helpers.arrayElement(events);
    const organizer = organizers.find(org => org.id === event.organizerId);
    const discountType = faker.helpers.arrayElement(discountTypes);
    
    // Generate code
    const code = faker.helpers.arrayElement([
      `${faker.word.adjective().toUpperCase()}${faker.number.int({ min: 10, max: 99 })}`,
      `${faker.company.buzzNoun().toUpperCase()}${faker.number.int({ min: 10, max: 99 })}`,
      `SAVE${faker.number.int({ min: 10, max: 50 })}`,
      `${event.title.substring(0, 4).toUpperCase()}${faker.number.int({ min: 10, max: 99 })}`
    ]);
    
    // Set discount value based on type
    const discountValue = discountType === DiscountType.PERCENTAGE
      ? faker.number.float({ min: 5, max: 50, precision: 0.01 })
      : faker.number.float({ min: 5, max: 50, precision: 0.01 });
    
    // Set dates
    const startDate = new Date();
    const endDate = new Date(event.startDate);
    
    await prisma.promoCode.create({
      data: {
        code,
        description: faker.lorem.sentence(),
        discountType,
        discountValue,
        maxUses: faker.number.int({ min: 10, max: 1000 }),
        usedCount: faker.number.int({ min: 0, max: 50 }),
        minPurchase: faker.datatype.boolean(0.3) ? faker.number.float({ min: 20, max: 100, precision: 0.01 }) : null,
        startDate,
        endDate,
        isActive: faker.datatype.boolean(0.8),
        createdBy: organizer.id,
        eventId: faker.datatype.boolean(0.7) ? event.id : null, // 70% are event-specific
      },
    });
  }
}

/**
 * Seed transactions
 */
async function seedTransactions(customers: any[], tickets: any[]) {
  const statusOptions = [TransactionStatus.COMPLETED, TransactionStatus.REFUNDED, TransactionStatus.PARTIALLY_REFUNDED];
  const paymentMethodOptions = [PaymentMethod.CREDIT_CARD, PaymentMethod.PAYPAL, PaymentMethod.APPLE_PAY];
  
  // Group available tickets by event for easier assignment
  const availableTickets = tickets.filter(ticket => ticket.status === TicketStatus.AVAILABLE);
  const ticketsByEvent = {};
  
  for (const ticket of availableTickets) {
    if (!ticketsByEvent[ticket.eventId]) {
      ticketsByEvent[ticket.eventId] = [];
    }
    ticketsByEvent[ticket.eventId].push(ticket);
  }
  
  for (let i = 0; i < NUM_TRANSACTIONS; i++) {
    const customer = faker.helpers.arrayElement(customers);
    const status = faker.helpers.arrayElement(statusOptions);
    const paymentMethod = faker.helpers.arrayElement(paymentMethodOptions);
    
    // Select an event and get its tickets
    const eventId = faker.helpers.arrayElement(Object.keys(ticketsByEvent));
    const eventTickets = ticketsByEvent[eventId];
    
    // Skip if no tickets available
    if (!eventTickets || eventTickets.length === 0) continue;
    
    // Select 1-4 tickets for this transaction
    const numTicketsToSelect = Math.min(faker.number.int({ min: 1, max: 4 }), eventTickets.length);
    const selectedTickets = [];
    
    for (let j = 0; j < numTicketsToSelect; j++) {
      if (eventTickets.length === 0) break;
      
      // Remove a ticket from available tickets
      const ticketIndex = faker.number.int({ min: 0, max: eventTickets.length - 1 });
      const selectedTicket = eventTickets.splice(ticketIndex, 1)[0];
      selectedTickets.push(selectedTicket);
    }
    
    // Skip if no tickets selected
    if (selectedTickets.length === 0) continue;
    
    // Calculate amount based on ticket types
    const ticketTypePromises = selectedTickets.map(ticket => 
      prisma.ticketType.findUnique({ where: { id: ticket.ticketTypeId } })
    );
    
    const ticketTypes = await Promise.all(ticketTypePromises);
    const amount = ticketTypes.reduce((sum, tt) => sum + Number(tt.price), 0);
    
    // Create transaction
    const transaction = await prisma.transaction.create({
      data: {
        userId: customer.id,
        amount,
        currency: ticketTypes[0].currency,
        status,
        paymentMethod,
        paymentIntentId: `pi_${faker.string.alphanumeric(24)}`,
        receiptUrl: `https://receipts.testticket.toy/${faker.string.alphanumeric(16)}`,
        refundAmount: status === TransactionStatus.REFUNDED ? amount : 
                     status === TransactionStatus.PARTIALLY_REFUNDED ? amount * faker.number.float({ min: 0.1, max: 0.9 }) : 
                     null,
        refundReason: status === TransactionStatus.REFUNDED || status === TransactionStatus.PARTIALLY_REFUNDED ? 
                     faker.helpers.arrayElement(['Customer request', 'Event cancelled', 'Event rescheduled', 'Duplicate purchase']) : 
                     null,
      },
    });
    
    // Update tickets with transaction ID and status
    for (const ticket of selectedTickets) {
      await prisma.ticket.update({
        where: { id: ticket.id },
        data: {
          userId: customer.id,
          transactionId: transaction.id,
          status: status === TransactionStatus.REFUNDED ? TicketStatus.REFUNDED : TicketStatus.SOLD,
          checkedInAt: faker.datatype.boolean(0.2) ? faker.date.recent() : null,
        },
      });
    }
  }
}

/**
 * Seed event analytics
 */
async function seedEventAnalytics(events: any[]) {
  for (const event of events) {
    // Skip draft events
    if (event.status === EventStatus.DRAFT) continue;
    
    const views = faker.number.int({ min: 100, max: 10000 });
    const uniqueVisitors = Math.floor(views * faker.number.float({ min: 0.6, max: 0.9 }));
    const checkoutStarts = Math.floor(uniqueVisitors * faker.number.float({ min: 0.1, max: 0.5 }));
    const checkoutCompletions = Math.floor(checkoutStarts * faker.number.float({ min: 0.3, max: 0.8 }));
    const revenue = faker.number.float({ min: 1000, max: 50000, precision: 0.01 });
    const refunds = faker.number.float({ min: 0, max: revenue * 0.2, precision: 0.01 });
    
    // Generate traffic sources
    const trafficSources = {
      'direct': faker.number.float({ min: 0.1, max: 0.4 }),
      'social': faker.number.float({ min: 0.1, max: 0.3 }),
      'search': faker.number.float({ min: 0.1, max: 0.3 }),
      'email': faker.number.float({ min: 0.05, max: 0.2 }),
      'referral': faker.number.float({ min: 0.05, max: 0.15 }),
    };
    
    // Normalize traffic sources to sum to 1
    const sum = Object.values(trafficSources).reduce((a, b) => a + b, 0);
    Object.keys(trafficSources).forEach(key => {
      trafficSources[key] = trafficSources[key] / sum;
    });
    
    // Generate daily stats for the last 30 days
    const dailyStats = {};
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      const dateString = date.toISOString().split('T')[0];
      
      dailyStats[dateString] = {
        views: faker.number.int({ min: 1, max: 500 }),
        uniqueVisitors: faker.number.int({ min: 1, max: 300 }),
        checkoutStarts: faker.number.int({ min: 0, max: 50 }),
        checkoutCompletions: faker.number.int({ min: 0, max: 30 }),
        revenue: faker.number.float({ min: 0, max: 2000, precision: 0.01 }),
      };
    }
    
    await prisma.eventAnalytics.create({
      data: {
        eventId: event.id,
        views,
        uniqueVisitors,
        checkoutStarts,
        checkoutCompletions,
        revenue,
        refunds,
        trafficSources,
        conversionRate: checkoutCompletions / views,
        dailyStats,
      },
    });
  }
}

/**
 * Seed marketing campaigns
 */
async function seedMarketingCampaigns(events: any[]) {
  const campaignTypes = Object.values(CampaignType);
  const campaignStatuses = Object.values(CampaignStatus);
  
  // Only create campaigns for published events
  const publishedEvents = events.filter(event => event.status === EventStatus.PUBLISHED);
  
  for (const event of publishedEvents) {
    // Each published event has 0-3 marketing campaigns
    const numCampaigns = faker.number.int({ min: 0, max: 3 });
    
    for (let i = 0; i < numCampaigns; i++) {
      const campaignType = faker.helpers.arrayElement(campaignTypes);
      const status = faker.helpers.arrayElement(campaignStatuses);
      
      // Campaign dates
      const startDate = new Date();
      const endDate = new Date(event.startDate);
      
      // Generate creative assets based on campaign type
      const creativeAssets = [];
      if (campaignType === CampaignType.EMAIL || campaignType === CampaignType.SOCIAL_MEDIA) {
        for (let j = 0; j < faker.number.int({ min: 1, max: 5 }); j++) {
          creativeAssets.push(faker.image.urlLoremFlickr({ category: 'event' }));
        }
      }
      
      // Generate target audience
      const targetAudience = {
        ageRange: faker.helpers.arrayElement(['18-24', '25-34', '35-44', '45-54', '55+']),
        locations: Array(faker.number.int({ min: 1, max: 3 }))
          .fill(null)
          .map(() => faker.location.city()),
        interests: Array(faker.number.int({ min: 1, max: 5 }))
          .fill(null)
          .map(() => faker.word.noun()),
      };
      
      // Generate results if campaign is active or completed
      let results = null;
      if (status === CampaignStatus.COMPLETED || status === CampaignStatus.ACTIVE) {
        results = {
          impressions: faker.number.int({ min: 1000, max: 100000 }),
          clicks: faker.number.int({ min: 100, max: 10000 }),
          conversions: faker.number.int({ min: 10, max: 1000 }),
          ctr: faker.number.float({ min: 0.01, max: 0.1, precision: 0.001 }),
          conversionRate: faker.number.float({ min: 0.01, max: 0.2, precision: 0.001 }),
          cost: faker.number.float({ min: 100, max: 5000, precision: 0.01 }),
          roi: faker.number.float({ min: -0.5, max: 10, precision: 0.01 }),
        };
      }
      
      await prisma.marketingCampaign.create({
        data: {
          name: `${event.title} - ${campaignType} Campaign`,
          description: faker.lorem.sentence(),
          eventId: event.id,
          campaignType,
          budget: faker.number.float({ min: 500, max: 10000, precision: 0.01 }),
          startDate,
          endDate,
          status,
          targetAudience,
          creativeAssets,
          results,
        },
      });
    }
  }
}

/**
 * Seed saved events
 */
async function seedSavedEvents(customers: any[], events: any[]) {
  // Each customer saves 0-5 events
  for (const customer of customers) {
    const numSavedEvents = faker.number.int({ min: 0, max: 5 });
    const savedEventIds = new Set();
    
    for (let i = 0; i < numSavedEvents; i++) {
      const event = faker.helpers.arrayElement(events);
      
      // Skip if already saved
      if (savedEventIds.has(event.id)) continue;
      savedEventIds.add(event.id);
      
      await prisma.savedEvent.create({
        data: {
          userId: customer.id,
          eventId: event.id,
        },
      });
    }
  }
}

/**
 * Seed reviews
 */
async function seedReviews(customers: any[], events: any[]) {
  // Only create reviews for published or completed events
  const reviewableEvents = events.filter(event => 
    event.status === EventStatus.PUBLISHED || event.status === EventStatus.COMPLETED
  );
  
  // Each customer reviews 0-3 events
  for (const customer of customers) {
    const numReviews = faker.number.int({ min: 0, max: 3 });
    const reviewedEventIds = new Set();
    
    for (let i = 0; i < numReviews; i++) {
      const event = faker.helpers.arrayElement(reviewableEvents);
      
      // Skip if already reviewed
      if (reviewedEventIds.has(event.id)) continue;
      reviewedEventIds.add(event.id);
      
      await prisma.review.create({
        data: {
          userId: customer.id,
          eventId: event.id,
          rating: faker.number.int({ min: 1, max: 5 }),
          comment: faker.datatype.boolean(0.8) ? faker.lorem.paragraph() : null,
          isPublic: faker.datatype.boolean(0.9),
        },
      });
    }
  }
}

/**
 * Seed notifications
 */
async function seedNotifications(users: any[]) {
  const notificationTypes = Object.values(NotificationType);
  
  // Create 1-5 notifications for each user
  for (const user of users) {
    const numNotifications = faker.number.int({ min: 1, max: 5 });
    
    for (let i = 0; i < numNotifications; i++) {
      const type = faker.helpers.arrayElement(notificationTypes);
      let title = '';
      let message = '';
      
      switch (type) {
        case NotificationType.EVENT_UPDATE:
          title = 'Event Update';
          message = `${faker.lorem.sentence()} has been updated.`;
          break;
        case NotificationType.TICKET_PURCHASE:
          title = 'Ticket Purchased';
          message = `Your ticket for ${faker.lorem.words(3)} has been confirmed.`;
          break;
        case NotificationType.PAYMENT_CONFIRMATION:
          title = 'Payment Confirmed';
          message = `Your payment of ${faker.finance.amount()} has been processed successfully.`;
          break;
        case NotificationType.REFUND:
          title = 'Refund Processed';
          message = `Your refund of ${faker.finance.amount()} has been processed.`;
          break;
        case NotificationType.REMINDER:
          title = 'Event Reminder';
          message = `${faker.lorem.sentence()} is happening soon.`;
          break;
        case NotificationType.SYSTEM:
          title = 'System Notification';
          message = faker.lorem.sentence();
          break;
      }
      
      await prisma.notification.create({
        data: {
          userId: user.id,
          title,
          message,
          type,
          isRead: faker.datatype.boolean(0.3),
          linkUrl: faker.datatype.boolean(0.7) ? `/events/${faker.string.uuid()}` : null,
        },
      });
    }
  }
}

/**
 * Seed blog posts
 */
async function seedBlogPosts(adminId: string) {
  const blogTitles = [
    'How to Host a Successful Virtual Event',
    'The Ultimate Guide to Event Marketing',
    '10 Tips for Selling Out Your Event',
    'Choosing the Perfect Venue for Your Event',
    'How to Price Your Event Tickets',
    'Creating an Engaging Event Experience',
    'The Future of Event Technology',
    'Sustainable Event Planning: A Complete Guide',
    'How to Measure Event Success',
    'Building a Community Around Your Events',
  ];
  
  for (let i = 0; i < NUM_BLOG_POSTS; i++) {
    const title = blogTitles[i] || faker.lorem.sentence();
    const slug = title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
    
    await prisma.blogPost.create({
      data: {
        title,
        slug,
        content: faker.lorem.paragraphs(10),
        excerpt: faker.lorem.paragraph(),
        coverImage: faker.image.urlLoremFlickr({ category: 'business' }),
        authorId: adminId,
        publishedAt: faker.datatype.boolean(0.8) ? faker.date.past() : null,
        tags: Array(faker.number.int({ min: 2, max: 5 }))
          .fill(null)
          .map(() => faker.helpers.arrayElement([
            'events', 'marketing', 'planning', 'technology', 'tickets', 
            'venues', 'virtual', 'promotion', 'sales', 'experience'
          ])),
      },
    });
  }
}

/**
 * Seed FAQ content
 */
async function seedFAQContent() {
  const faqCategories = [
    { title: 'General Questions', order: 1 },
    { title: 'Tickets & Purchases', order: 2 },
    { title: 'For Event Organizers', order: 3 },
    { title: 'Account & Profile', order: 4 },
    { title: 'Technical Support', order: 5 },
  ];
  
  for (const categoryData of faqCategories) {
    const category = await prisma.faqCategory.create({
      data: {
        title: categoryData.title,
        order: categoryData.order,
      },
    });
    
    // Create FAQ items for this category
    for (let i = 0; i < NUM_FAQ_ITEMS_PER_CATEGORY; i++) {
      let question = '';
      let answer = '';
      
      switch (categoryData.title) {
        case 'General Questions':
          question = faker.helpers.arrayElement([
            'What is TestTicket Toy?',
            'How do I contact customer support?',
            'Is TestTicket Toy available in my country?',
            'What payment methods do you accept?',
          ]);
          break;
        case 'Tickets & Purchases':
          question = faker.helpers.arrayElement([
            'How do I get my tickets after purchase?',
            'Can I get a refund for my tickets?',
            'How do I transfer tickets to someone else?',
            'What happens if an event is cancelled?',
          ]);
          break;
        case 'For Event Organizers':
          question = faker.helpers.arrayElement([
            'How do I create an event?',
            'What fees does TestTicket Toy charge?',
            'When do I get paid for ticket sales?',
            'How do I promote my event?',
          ]);
          break;
        case 'Account & Profile':
          question = faker.helpers.arrayElement([
            'How do I create an account?',
            'How do I reset my password?',
            'Can I change my email address?',
            'How do I delete my account?',
          ]);
          break;
        case 'Technical Support':
          question = faker.helpers.arrayElement([
            'The website is not loading properly, what should I do?',
            'I can\'t upload images for my event, what\'s wrong?',
            'How do I scan tickets at my event?',
            'The payment process failed, what now?',
          ]);
          break;
      }
      
      answer = faker.lorem.paragraphs(2);
      
      await prisma.faqItem.create({
        data: {
          question,
          answer,
          order: i + 1,
          categoryId: category.id,
        },
      });
    }
  }
}

/**
 * Seed support tickets
 */
async function seedSupportTickets(customers: any[]) {
  const supportTicketStatuses = Object.values(SupportTicketStatus);
  const ticketPriorities = Object.values(TicketPriority);
  
  for (let i = 0; i < NUM_SUPPORT_TICKETS; i++) {
    const customer = faker.helpers.arrayElement(customers);
    const status = faker.helpers.arrayElement(supportTicketStatuses);
    const priority = faker.helpers.arrayElement(ticketPriorities);
    
    const supportTicket = await prisma.supportTicket.create({
      data: {
        subject: faker.helpers.arrayElement([
          'Problem with ticket purchase',
          'Can\'t access my account',
          'Event refund request',
          'Question about organizer fees',
          'Technical issue with the platform',
          'Need help with event creation',
          'Payment failed',
          'How do I transfer my ticket?',
        ]),
        description: faker.lorem.paragraphs(2),
        status,
        priority,
        email: customer.email,
        name: `${customer.firstName} ${customer.lastName}`,
        userId: customer.id,
      },
    });
    
    // Add 1-3 responses if ticket is not open
    if (status !== SupportTicketStatus.OPEN) {
      const numResponses = faker.number.int({ min: 1, max: 3 });
      
      for (let j = 0; j < numResponses; j++) {
        const isStaff = j % 2 === 0; // Alternate between staff and customer
        
        await prisma.supportResponse.create({
          data: {
            supportTicketId: supportTicket.id,
            message: faker.lorem.paragraphs(1),
            respondentName: isStaff ? faker.helpers.arrayElement(['Support Team', 'Customer Service', 'Technical Support']) : `${customer.firstName} ${customer.lastName}`,
            isStaff,
          },
        });
      }
    }
  }
}

// Execute the main function
main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    // Disconnect Prisma client
    await prisma.$disconnect();
  });
