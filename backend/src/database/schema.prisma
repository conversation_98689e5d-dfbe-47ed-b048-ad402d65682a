// This is the Prisma schema for TestTicket Toy platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - Base for both customers and organizers
model User {
  id                String    @id @default(uuid())
  email             String    @unique
  password          String
  firstName         String
  lastName          String
  phoneNumber       String?
  profileImage      String?
  role              UserRole  @default(CUSTOMER)
  isEmailVerified   Boolean   @default(false)
  verificationToken String?
  resetToken        String?
  resetTokenExpiry  DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Customer relationships
  tickets        Ticket[]
  transactions   Transaction[]
  reviews        Review[]
  savedEvents    SavedEvent[]
  notifications  Notification[]

  // Organizer relationships
  organizerProfile OrganizerProfile?
  events           Event[]
  promoCodes       PromoCode[]
  teamMembers      TeamMember[]

  @@map("users")
}

enum UserRole {
  CUSTOMER
  ORGANIZER
  ADMIN
}

// Organizer profile with tier system
model OrganizerProfile {
  id                String          @id @default(uuid())
  userId            String          @unique
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyName       String
  companyLogo       String?
  description       String?
  website           String?
  socialLinks       Json?           // Stores social media links as JSON
  tier              OrganizerTier   @default(STARTER)
  ticketsSold       Int             @default(0)
  totalRevenue      Decimal         @default(0) @db.Decimal(10, 2)
  stripeAccountId   String?
  paypalAccountId   String?
  bankAccountInfo   Json?           // Stores bank account details as JSON
  verificationStatus VerificationStatus @default(PENDING)
  verificationDocuments Json?       // Stores document URLs as JSON
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@map("organizer_profiles")
}

enum OrganizerTier {
  STARTER
  GROWTH
  PROFESSIONAL
  ENTERPRISE
}

enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

// Team members for organizer accounts
model TeamMember {
  id           String       @id @default(uuid())
  email        String
  role         TeamRole
  permissions  Json         // Stores permissions as JSON array
  invitedBy    String
  user         User         @relation(fields: [invitedBy], references: [id])
  inviteToken  String?
  inviteAccepted Boolean    @default(false)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@map("team_members")
}

enum TeamRole {
  ADMIN
  EDITOR
  ANALYST
  SUPPORT
}

// Events model
model Event {
  id                String      @id @default(uuid())
  title             String
  slug              String      @unique
  description       String
  shortDescription  String?
  bannerImage       String?
  galleryImages     String[]
  startDate         DateTime
  endDate           DateTime
  timezone          String
  isOnline          Boolean     @default(false)
  venue             String?
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?
  latitude          Float?
  longitude         Float?
  currency          String      @default("USD")
  organizerId       String
  organizer         User        @relation(fields: [organizerId], references: [id])
  category          Category    @relation(fields: [categoryId], references: [id])
  categoryId        String
  status            EventStatus @default(DRAFT)
  visibility        Visibility  @default(PUBLIC)
  maxTicketsPerUser Int?
  termsAndConditions String?
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relationships
  ticketTypes      TicketType[]
  promoCodes       PromoCode[]
  analytics        EventAnalytics?
  savedEvents      SavedEvent[]
  reviews          Review[]
  marketingCampaigns MarketingCampaign[]

  @@map("events")
}

enum EventStatus {
  DRAFT
  PUBLISHED
  CANCELLED
  COMPLETED
}

enum Visibility {
  PUBLIC
  PRIVATE
  UNLISTED
}

// Event categories
model Category {
  id          String   @id @default(uuid())
  name        String   @unique
  slug        String   @unique
  description String?
  icon        String?
  color       String?
  events      Event[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("categories")
}

// Ticket types for events
model TicketType {
  id                String    @id @default(uuid())
  name              String
  description       String?
  price             Decimal   @db.Decimal(10, 2)
  currency          String    @default("USD")
  quantity          Int
  quantitySold      Int       @default(0)
  eventId           String
  event             Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  salesStartDate    DateTime
  salesEndDate      DateTime
  isEarlyBird       Boolean   @default(false)
  isVIP             Boolean   @default(false)
  maxPerUser        Int?
  includesAdditionalItems Json?      // Additional items included with ticket
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relationships
  tickets           Ticket[]

  @@map("ticket_types")
}

// Individual tickets
model Ticket {
  id              String        @id @default(uuid())
  ticketNumber    String        @unique
  ticketTypeId    String
  ticketType      TicketType    @relation(fields: [ticketTypeId], references: [id])
  eventId         String
  userId          String?       // Null if ticket not yet purchased
  user            User?         @relation(fields: [userId], references: [id])
  transactionId   String?
  transaction     Transaction?  @relation(fields: [transactionId], references: [id])
  status          TicketStatus  @default(AVAILABLE)
  qrCode          String?
  checkedInAt     DateTime?
  transferredFrom String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("tickets")
}

enum TicketStatus {
  AVAILABLE
  RESERVED
  SOLD
  CHECKED_IN
  CANCELLED
  REFUNDED
}

// Transactions for ticket purchases
model Transaction {
  id                String            @id @default(uuid())
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  amount            Decimal           @db.Decimal(10, 2)
  currency          String            @default("USD")
  status            TransactionStatus @default(PENDING)
  paymentMethod     PaymentMethod
  paymentIntentId   String?           // For Stripe
  paymentOrderId    String?           // For PayPal
  receiptUrl        String?
  refundAmount      Decimal?          @db.Decimal(10, 2)
  refundReason      String?
  promoCodeId       String?
  promoCode         PromoCode?        @relation(fields: [promoCodeId], references: [id])
  tickets           Ticket[]
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("transactions")
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentMethod {
  CREDIT_CARD
  PAYPAL
  APPLE_PAY
  GOOGLE_PAY
  BANK_TRANSFER
  OTHER
}

// Promo codes
model PromoCode {
  id              String          @id @default(uuid())
  code            String          @unique
  description     String?
  discountType    DiscountType
  discountValue   Decimal         @db.Decimal(10, 2)
  maxUses         Int?
  usedCount       Int             @default(0)
  minPurchase     Decimal?        @db.Decimal(10, 2)
  startDate       DateTime
  endDate         DateTime
  isActive        Boolean         @default(true)
  createdBy       String
  creator         User            @relation(fields: [createdBy], references: [id])
  eventId         String?
  event           Event?          @relation(fields: [eventId], references: [id])
  transactions    Transaction[]
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("promo_codes")
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

// Event analytics
model EventAnalytics {
  id                String    @id @default(uuid())
  eventId           String    @unique
  event             Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  views             Int       @default(0)
  uniqueVisitors    Int       @default(0)
  checkoutStarts    Int       @default(0)
  checkoutCompletions Int     @default(0)
  revenue           Decimal   @default(0) @db.Decimal(10, 2)
  refunds           Decimal   @default(0) @db.Decimal(10, 2)
  trafficSources    Json?     // Stores traffic source data as JSON
  conversionRate    Float?
  dailyStats        Json?     // Stores daily analytics as JSON
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@map("event_analytics")
}

// Marketing campaigns
model MarketingCampaign {
  id              String            @id @default(uuid())
  name            String
  description     String?
  eventId         String
  event           Event             @relation(fields: [eventId], references: [id])
  campaignType    CampaignType
  budget          Decimal?          @db.Decimal(10, 2)
  startDate       DateTime
  endDate         DateTime
  status          CampaignStatus    @default(DRAFT)
  targetAudience  Json?             // Stores audience targeting as JSON
  creativeAssets  String[]          // URLs to creative assets
  results         Json?             // Campaign results as JSON
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("marketing_campaigns")
}

enum CampaignType {
  EMAIL
  SOCIAL_MEDIA
  SEARCH_ADS
  DISPLAY_ADS
  AFFILIATE
  INFLUENCER
}

enum CampaignStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

// User saved events
model SavedEvent {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  eventId   String
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, eventId])
  @@map("saved_events")
}

// Event reviews
model Review {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  eventId   String
  event     Event    @relation(fields: [eventId], references: [id])
  rating    Int      // 1-5 stars
  comment   String?
  isPublic  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, eventId])
  @@map("reviews")
}

// User notifications
model Notification {
  id          String           @id @default(uuid())
  userId      String
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  title       String
  message     String
  type        NotificationType
  isRead      Boolean          @default(false)
  linkUrl     String?
  createdAt   DateTime         @default(now())

  @@map("notifications")
}

enum NotificationType {
  EVENT_UPDATE
  TICKET_PURCHASE
  PAYMENT_CONFIRMATION
  REFUND
  REMINDER
  SYSTEM
}

// Blog posts
model BlogPost {
  id          String   @id @default(uuid())
  title       String
  slug        String   @unique
  content     String
  excerpt     String?
  coverImage  String?
  authorId    String
  publishedAt DateTime?
  tags        String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("blog_posts")
}

// Support tickets
model SupportTicket {
  id          String           @id @default(uuid())
  subject     String
  description String
  status      SupportTicketStatus @default(OPEN)
  priority    TicketPriority   @default(MEDIUM)
  email       String
  name        String
  userId      String?
  responses   SupportResponse[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@map("support_tickets")
}

model SupportResponse {
  id              String   @id @default(uuid())
  supportTicketId String
  supportTicket   SupportTicket @relation(fields: [supportTicketId], references: [id], onDelete: Cascade)
  message         String
  respondentName  String
  isStaff         Boolean  @default(false)
  createdAt       DateTime @default(now())

  @@map("support_responses")
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// FAQ categories and items
model FaqCategory {
  id        String    @id @default(uuid())
  title     String
  order     Int       @default(0)
  faqItems  FaqItem[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@map("faq_categories")
}

model FaqItem {
  id            String      @id @default(uuid())
  question      String
  answer        String
  order         Int         @default(0)
  categoryId    String
  category      FaqCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("faq_items")
}
