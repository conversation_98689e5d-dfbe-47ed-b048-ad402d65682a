import express from 'express';
import { analyticsController } from '../controllers/analytics.controller';
import { authMiddleware, restrictTo } from '../middleware/auth.middleware';
import { UserRole } from '@prisma/client';
import { validate } from '../middleware/validate.middleware';
import { body, param, query } from 'express-validator';

const router = express.Router();

/**
 * @route   GET /api/v1/analytics/dashboard
 * @desc    Get dashboard analytics for organizer
 * @access  Private (Organizer, Admin)
 */
router.get(
  '/dashboard',
  authMiddleware,
  restrictTo([UserRole.ORGANIZER, UserRole.ADMIN]),
  analyticsController.getDashboardAnalytics
);

/**
 * @route   GET /api/v1/analytics/events/:eventId
 * @desc    Get analytics for a specific event
 * @access  Private (Organizer, Admin)
 */
router.get(
  '/events/:eventId',
  authMiddleware,
  restrictTo([UserRole.ORGANIZER, UserRole.ADMIN]),
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
    query('period').optional().isIn(['7days', '30days', '90days', '12months', 'alltime']).withMessage('Invalid period'),
  ],
  validate,
  analyticsController.getEventAnalytics
);

/**
 * @route   POST /api/v1/analytics/events/:eventId/track-view
 * @desc    Track event view for analytics
 * @access  Public
 */
router.post(
  '/events/:eventId/track-view',
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
    body('referrer').optional().isString(),
    body('uniqueId').optional().isString(),
  ],
  validate,
  analyticsController.trackEventView
);

/**
 * @route   POST /api/v1/analytics/events/:eventId/track-checkout-start
 * @desc    Track checkout start for analytics
 * @access  Public
 */
router.post(
  '/events/:eventId/track-checkout-start',
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
  ],
  validate,
  analyticsController.trackCheckoutStart
);

/**
 * @route   POST /api/v1/analytics/events/:eventId/track-checkout-completion
 * @desc    Track checkout completion for analytics
 * @access  Public
 */
router.post(
  '/events/:eventId/track-checkout-completion',
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
  ],
  validate,
  analyticsController.trackCheckoutCompletion
);

/**
 * @route   POST /api/v1/analytics/comparative
 * @desc    Get comparative analytics between events
 * @access  Private (Organizer, Admin)
 */
router.post(
  '/comparative',
  authMiddleware,
  restrictTo([UserRole.ORGANIZER, UserRole.ADMIN]),
  [
    body('eventIds').isArray().withMessage('Event IDs must be an array'),
    body('eventIds.*').isUUID().withMessage('Invalid event ID format'),
  ],
  validate,
  analyticsController.getComparativeAnalytics
);

/**
 * @route   GET /api/v1/analytics/events/:eventId/export
 * @desc    Export analytics data
 * @access  Private (Organizer, Admin)
 */
router.get(
  '/events/:eventId/export',
  authMiddleware,
  restrictTo([UserRole.ORGANIZER, UserRole.ADMIN]),
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
    query('format').optional().isIn(['json', 'csv']).withMessage('Invalid format'),
  ],
  validate,
  analyticsController.exportAnalytics
);

/**
 * @route   GET /api/v1/analytics/events/:eventId/demographics
 * @desc    Get attendee demographics
 * @access  Private (Organizer, Admin)
 */
router.get(
  '/events/:eventId/demographics',
  authMiddleware,
  restrictTo([UserRole.ORGANIZER, UserRole.ADMIN]),
  [
    param('eventId').isUUID().withMessage('Invalid event ID format'),
  ],
  validate,
  analyticsController.getAttendeeDemographics
);

export default router;
