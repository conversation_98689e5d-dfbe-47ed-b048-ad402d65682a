import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import morgan from 'morgan';
import { rateLimit } from 'express-rate-limit';
import { PrismaClient } from '@prisma/client';
import { createClient } from 'redis';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Import routes
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import eventRoutes from './routes/event.routes';
import ticketRoutes from './routes/ticket.routes';
import organizerRoutes from './routes/organizer.routes';
import paymentRoutes from './routes/payment.routes';
import promoCodeRoutes from './routes/promoCode.routes';
import analyticsRoutes from './routes/analytics.routes';
import marketingRoutes from './routes/marketing.routes';
import blogRoutes from './routes/blog.routes';
import supportRoutes from './routes/support.routes';
import faqRoutes from './routes/faq.routes';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth.middleware';

// Import utils
import logger from './utils/logger';

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize Redis client if URL is provided
let redisClient: any = null;
if (process.env.REDIS_URL) {
  redisClient = createClient({
    url: process.env.REDIS_URL,
  });
  
  redisClient.on('error', (err: Error) => {
    logger.error('Redis Client Error', err);
  });
  
  redisClient.connect().catch((err: Error) => {
    logger.error('Redis Connection Error', err);
  });
}

// Create Express app
const app = express();

// Set up rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes by default
  max: parseInt(process.env.RATE_LIMIT_MAX || '100'), // Limit each IP to 100 requests per window
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again later.',
});

// Apply middleware
app.use(helmet()); // Security headers
app.use(compression()); // Compress responses
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Parse URL-encoded bodies
app.use(cookieParser(process.env.COOKIE_SECRET)); // Parse cookies
app.use(morgan('dev')); // HTTP request logger
app.use(limiter); // Apply rate limiting

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Serve static files from uploads directory
app.use('/uploads', express.static(uploadsDir));

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/users', authMiddleware, userRoutes);
app.use('/api/v1/events', eventRoutes); // Some endpoints public, some protected
app.use('/api/v1/tickets', ticketRoutes); // Some endpoints public, some protected
app.use('/api/v1/organizers', organizerRoutes); // Some endpoints public, some protected
app.use('/api/v1/payments', authMiddleware, paymentRoutes);
app.use('/api/v1/promo-codes', promoCodeRoutes); // Some endpoints public, some protected
app.use('/api/v1/analytics', authMiddleware, analyticsRoutes);
app.use('/api/v1/marketing', authMiddleware, marketingRoutes);
app.use('/api/v1/blog', blogRoutes); // Public endpoints
app.use('/api/v1/support', supportRoutes); // Public endpoints
app.use('/api/v1/faq', faqRoutes); // Public endpoints

// Stripe webhook endpoint (needs raw body)
app.post('/api/v1/webhooks/stripe', express.raw({ type: 'application/json' }), paymentRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 4000;
const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  logger.error('Unhandled Rejection:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  logger.error('Uncaught Exception:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(async () => {
    logger.info('Process terminated');
    await prisma.$disconnect();
    if (redisClient) {
      await redisClient.disconnect();
    }
    process.exit(0);
  });
});

export { app, prisma, redisClient };
