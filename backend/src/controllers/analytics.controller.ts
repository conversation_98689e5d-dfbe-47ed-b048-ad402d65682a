import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { ApiError } from '../middleware/errorHandler';
import logger from '../utils/logger';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Analytics controller for handling event and organizer analytics
 */
export const analyticsController = {
  /**
   * Get dashboard analytics for an organizer
   * Provides overview metrics for all events
   */
  getDashboardAnalytics: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const organizerId = req.user?.id;

      if (!organizerId) {
        throw new ApiError(401, 'Unauthorized');
      }

      // Get date range from query params or default to last 30 days
      const endDate = new Date();
      const startDate = new Date();
      const period = req.query.period as string || '30days';

      switch (period) {
        case '7days':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30days':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90days':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '12months':
          startDate.setMonth(endDate.getMonth() - 12);
          break;
        case 'alltime':
          startDate.setFullYear(2000); // Effectively all time
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Get all events for the organizer
      const events = await prisma.event.findMany({
        where: {
          organizerId,
        },
        include: {
          ticketTypes: true,
          analytics: true,
        },
      });

      // Get all transactions for the organizer's events
      const eventIds = events.map(event => event.id);
      
      const transactions = await prisma.transaction.findMany({
        where: {
          tickets: {
            some: {
              eventId: {
                in: eventIds,
              },
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
        },
        include: {
          tickets: {
            include: {
              ticketType: true,
            },
          },
        },
      });

      // Calculate revenue
      const totalRevenue = transactions.reduce((sum, transaction) => {
        // Only count completed transactions
        if (transaction.status === 'COMPLETED') {
          return sum + Number(transaction.amount);
        }
        return sum;
      }, 0);

      // Calculate refunds
      const totalRefunds = transactions.reduce((sum, transaction) => {
        // Only count refunded or partially refunded transactions
        if (transaction.status === 'REFUNDED' || transaction.status === 'PARTIALLY_REFUNDED') {
          return sum + Number(transaction.refundAmount || 0);
        }
        return sum;
      }, 0);

      // Calculate net revenue
      const netRevenue = totalRevenue - totalRefunds;

      // Count tickets sold
      const ticketsSold = await prisma.ticket.count({
        where: {
          eventId: {
            in: eventIds,
          },
          status: 'SOLD',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Get previous period data for comparison
      const previousStartDate = new Date(startDate);
      const previousEndDate = new Date(endDate);
      const periodDuration = endDate.getTime() - startDate.getTime();
      
      previousStartDate.setTime(previousStartDate.getTime() - periodDuration);
      previousEndDate.setTime(previousEndDate.getTime() - periodDuration);

      const previousTransactions = await prisma.transaction.findMany({
        where: {
          tickets: {
            some: {
              eventId: {
                in: eventIds,
              },
              createdAt: {
                gte: previousStartDate,
                lte: previousEndDate,
              },
            },
          },
        },
      });

      // Calculate previous period revenue
      const previousRevenue = previousTransactions.reduce((sum, transaction) => {
        if (transaction.status === 'COMPLETED') {
          return sum + Number(transaction.amount);
        }
        return sum;
      }, 0);

      // Calculate previous period refunds
      const previousRefunds = previousTransactions.reduce((sum, transaction) => {
        if (transaction.status === 'REFUNDED' || transaction.status === 'PARTIALLY_REFUNDED') {
          return sum + Number(transaction.refundAmount || 0);
        }
        return sum;
      }, 0);

      // Calculate previous net revenue
      const previousNetRevenue = previousRevenue - previousRefunds;

      // Count previous period tickets sold
      const previousTicketsSold = await prisma.ticket.count({
        where: {
          eventId: {
            in: eventIds,
          },
          status: 'SOLD',
          createdAt: {
            gte: previousStartDate,
            lte: previousEndDate,
          },
        },
      });

      // Calculate daily sales data for chart
      const dailySalesData = await getDailySalesData(eventIds, startDate, endDate);

      // Get top selling events
      const topEvents = await getTopSellingEvents(eventIds, startDate, endDate);

      // Get ticket type distribution
      const ticketTypeDistribution = await getTicketTypeDistribution(eventIds, startDate, endDate);

      // Get traffic sources
      const trafficSources = await getTrafficSources(eventIds);

      // Get upcoming events
      const upcomingEvents = await prisma.event.count({
        where: {
          organizerId,
          startDate: {
            gt: new Date(),
          },
          status: 'PUBLISHED',
        },
      });

      // Get previous period upcoming events
      const previousUpcomingEvents = await prisma.event.count({
        where: {
          organizerId,
          startDate: {
            gt: previousStartDate,
            lte: new Date(),
          },
          status: 'PUBLISHED',
        },
      });

      // Get total attendees (unique customers who purchased tickets)
      const attendees = await prisma.ticket.findMany({
        where: {
          eventId: {
            in: eventIds,
          },
          status: 'SOLD',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          userId: true,
        },
        distinct: ['userId'],
      });

      const totalAttendees = attendees.length;

      // Get previous period attendees
      const previousAttendees = await prisma.ticket.findMany({
        where: {
          eventId: {
            in: eventIds,
          },
          status: 'SOLD',
          createdAt: {
            gte: previousStartDate,
            lte: previousEndDate,
          },
        },
        select: {
          userId: true,
        },
        distinct: ['userId'],
      });

      const previousTotalAttendees = previousAttendees.length;

      // Return analytics data
      res.status(200).json({
        success: true,
        data: {
          period,
          startDate,
          endDate,
          revenue: {
            total: totalRevenue,
            refunds: totalRefunds,
            net: netRevenue,
            previousNet: previousNetRevenue,
            percentChange: previousNetRevenue > 0 
              ? ((netRevenue - previousNetRevenue) / previousNetRevenue) * 100 
              : netRevenue > 0 ? 100 : 0,
          },
          tickets: {
            sold: ticketsSold,
            previousSold: previousTicketsSold,
            percentChange: previousTicketsSold > 0 
              ? ((ticketsSold - previousTicketsSold) / previousTicketsSold) * 100 
              : ticketsSold > 0 ? 100 : 0,
            typeDistribution: ticketTypeDistribution,
          },
          events: {
            upcoming: upcomingEvents,
            previousUpcoming: previousUpcomingEvents,
            percentChange: previousUpcomingEvents > 0 
              ? ((upcomingEvents - previousUpcomingEvents) / previousUpcomingEvents) * 100 
              : upcomingEvents > 0 ? 100 : 0,
            topSelling: topEvents,
          },
          attendees: {
            total: totalAttendees,
            previousTotal: previousTotalAttendees,
            percentChange: previousTotalAttendees > 0 
              ? ((totalAttendees - previousTotalAttendees) / previousTotalAttendees) * 100 
              : totalAttendees > 0 ? 100 : 0,
          },
          dailySales: dailySalesData,
          trafficSources,
        },
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get analytics for a specific event
   */
  getEventAnalytics: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;
      const organizerId = req.user?.id;

      if (!organizerId) {
        throw new ApiError(401, 'Unauthorized');
      }

      // Verify the event belongs to the organizer
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
          organizerId,
        },
        include: {
          ticketTypes: true,
          analytics: true,
          category: true,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get date range from query params or default to all time for the event
      const endDate = new Date();
      let startDate = new Date(event.createdAt);
      const period = req.query.period as string || 'alltime';

      switch (period) {
        case '7days':
          startDate = new Date();
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30days':
          startDate = new Date();
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90days':
          startDate = new Date();
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '12months':
          startDate = new Date();
          startDate.setMonth(endDate.getMonth() - 12);
          break;
        case 'alltime':
          // Already set to event creation date
          break;
        default:
          // Already set to event creation date
      }

      // Get transactions for this event
      const transactions = await prisma.transaction.findMany({
        where: {
          tickets: {
            some: {
              eventId,
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
        },
        include: {
          tickets: {
            include: {
              ticketType: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Calculate revenue
      const totalRevenue = transactions.reduce((sum, transaction) => {
        if (transaction.status === 'COMPLETED') {
          return sum + Number(transaction.amount);
        }
        return sum;
      }, 0);

      // Calculate refunds
      const totalRefunds = transactions.reduce((sum, transaction) => {
        if (transaction.status === 'REFUNDED' || transaction.status === 'PARTIALLY_REFUNDED') {
          return sum + Number(transaction.refundAmount || 0);
        }
        return sum;
      }, 0);

      // Calculate net revenue
      const netRevenue = totalRevenue - totalRefunds;

      // Get ticket sales by type
      const ticketSalesByType = await prisma.ticket.groupBy({
        by: ['ticketTypeId', 'status'],
        where: {
          eventId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Format ticket sales by type
      const ticketTypes = event.ticketTypes.reduce((acc, type) => {
        acc[type.id] = {
          id: type.id,
          name: type.name,
          price: type.price,
          sold: 0,
          available: type.quantity - type.quantitySold,
          revenue: 0,
        };
        return acc;
      }, {} as Record<string, any>);

      // Update ticket sales counts
      ticketSalesByType.forEach((sale) => {
        if (sale.status === 'SOLD' && ticketTypes[sale.ticketTypeId]) {
          ticketTypes[sale.ticketTypeId].sold = sale._count.id;
          ticketTypes[sale.ticketTypeId].revenue = 
            Number(ticketTypes[sale.ticketTypeId].price) * sale._count.id;
        }
      });

      // Get daily sales data
      const dailySalesData = await getDailySalesData([eventId], startDate, endDate);

      // Get hourly sales distribution for peak times analysis
      const hourlySalesDistribution = await getHourlySalesDistribution(eventId, startDate, endDate);

      // Get geographic distribution of attendees
      const geographicDistribution = await getGeographicDistribution(eventId);

      // Get event analytics or create if it doesn't exist
      let analytics = event.analytics;
      
      if (!analytics) {
        analytics = await prisma.eventAnalytics.create({
          data: {
            eventId,
            views: 0,
            uniqueVisitors: 0,
            checkoutStarts: 0,
            checkoutCompletions: 0,
            revenue: 0,
            refunds: 0,
          },
        });
      }

      // Calculate conversion rate
      const conversionRate = analytics.uniqueVisitors > 0 
        ? (analytics.checkoutCompletions / analytics.uniqueVisitors) * 100 
        : 0;

      // Return event analytics data
      res.status(200).json({
        success: true,
        data: {
          event: {
            id: event.id,
            title: event.title,
            startDate: event.startDate,
            endDate: event.endDate,
            venue: event.venue,
            city: event.city,
            country: event.country,
            category: event.category,
            status: event.status,
          },
          period,
          startDate,
          endDate,
          revenue: {
            total: totalRevenue,
            refunds: totalRefunds,
            net: netRevenue,
          },
          tickets: {
            byType: Object.values(ticketTypes),
            totalSold: Object.values(ticketTypes).reduce((sum, type) => sum + type.sold, 0),
            totalAvailable: Object.values(ticketTypes).reduce((sum, type) => sum + type.available, 0),
          },
          traffic: {
            views: analytics.views,
            uniqueVisitors: analytics.uniqueVisitors,
            checkoutStarts: analytics.checkoutStarts,
            checkoutCompletions: analytics.checkoutCompletions,
            conversionRate,
            sources: analytics.trafficSources || {},
          },
          dailySales: dailySalesData,
          hourlySales: hourlySalesDistribution,
          geography: geographicDistribution,
          transactions: transactions.map(t => ({
            id: t.id,
            date: t.createdAt,
            customer: t.user ? `${t.user.firstName} ${t.user.lastName}` : 'Anonymous',
            email: t.user?.email,
            amount: t.amount,
            status: t.status,
            ticketCount: t.tickets.length,
          })),
        },
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Track event view for analytics
   */
  trackEventView: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;
      const { referrer, uniqueId } = req.body;

      // Verify the event exists
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get or create analytics record
      let analytics = await prisma.eventAnalytics.findUnique({
        where: {
          eventId,
        },
      });

      if (!analytics) {
        analytics = await prisma.eventAnalytics.create({
          data: {
            eventId,
            views: 1,
            uniqueVisitors: 1,
            checkoutStarts: 0,
            checkoutCompletions: 0,
            revenue: 0,
            refunds: 0,
            trafficSources: {
              direct: 0,
              search: 0,
              social: 0,
              email: 0,
              referral: 0,
              other: 0,
              [getReferrerType(referrer)]: 1,
            },
          },
        });
      } else {
        // Determine traffic source
        const trafficSource = getReferrerType(referrer);
        const currentSources = analytics.trafficSources as Record<string, number> || {
          direct: 0,
          search: 0,
          social: 0,
          email: 0,
          referral: 0,
          other: 0,
        };

        // Update analytics
        await prisma.eventAnalytics.update({
          where: {
            id: analytics.id,
          },
          data: {
            views: {
              increment: 1,
            },
            // Only increment unique visitors if this is a new visitor
            uniqueVisitors: uniqueId ? {
              increment: 1,
            } : undefined,
            trafficSources: {
              ...currentSources,
              [trafficSource]: (currentSources[trafficSource] || 0) + 1,
            },
          },
        });
      }

      res.status(200).json({
        success: true,
        message: 'View tracked successfully',
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Track checkout start for analytics
   */
  trackCheckoutStart: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;

      // Verify the event exists
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get or create analytics record
      let analytics = await prisma.eventAnalytics.findUnique({
        where: {
          eventId,
        },
      });

      if (!analytics) {
        analytics = await prisma.eventAnalytics.create({
          data: {
            eventId,
            views: 0,
            uniqueVisitors: 0,
            checkoutStarts: 1,
            checkoutCompletions: 0,
            revenue: 0,
            refunds: 0,
          },
        });
      } else {
        // Update analytics
        await prisma.eventAnalytics.update({
          where: {
            id: analytics.id,
          },
          data: {
            checkoutStarts: {
              increment: 1,
            },
          },
        });
      }

      res.status(200).json({
        success: true,
        message: 'Checkout start tracked successfully',
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Track checkout completion for analytics
   */
  trackCheckoutCompletion: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;

      // Verify the event exists
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get or create analytics record
      let analytics = await prisma.eventAnalytics.findUnique({
        where: {
          eventId,
        },
      });

      if (!analytics) {
        analytics = await prisma.eventAnalytics.create({
          data: {
            eventId,
            views: 0,
            uniqueVisitors: 0,
            checkoutStarts: 0,
            checkoutCompletions: 1,
            revenue: 0,
            refunds: 0,
          },
        });
      } else {
        // Update analytics
        await prisma.eventAnalytics.update({
          where: {
            id: analytics.id,
          },
          data: {
            checkoutCompletions: {
              increment: 1,
            },
          },
        });
      }

      res.status(200).json({
        success: true,
        message: 'Checkout completion tracked successfully',
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get comparative analytics between events
   */
  getComparativeAnalytics: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventIds } = req.body;
      const organizerId = req.user?.id;

      if (!organizerId) {
        throw new ApiError(401, 'Unauthorized');
      }

      if (!eventIds || !Array.isArray(eventIds) || eventIds.length === 0) {
        throw new ApiError(400, 'Event IDs are required');
      }

      // Verify all events belong to the organizer
      const events = await prisma.event.findMany({
        where: {
          id: {
            in: eventIds,
          },
          organizerId,
        },
        include: {
          ticketTypes: true,
          analytics: true,
          category: true,
        },
      });

      if (events.length !== eventIds.length) {
        throw new ApiError(404, 'One or more events not found or not owned by you');
      }

      // Get all transactions for these events
      const transactions = await prisma.transaction.findMany({
        where: {
          tickets: {
            some: {
              eventId: {
                in: eventIds,
              },
            },
          },
        },
        include: {
          tickets: {
            include: {
              ticketType: true,
            },
          },
        },
      });

      // Prepare comparative data for each event
      const comparativeData = events.map(event => {
        // Filter transactions for this event
        const eventTransactions = transactions.filter(t => 
          t.tickets.some(ticket => ticket.eventId === event.id)
        );

        // Calculate revenue
        const totalRevenue = eventTransactions.reduce((sum, transaction) => {
          if (transaction.status === 'COMPLETED') {
            return sum + Number(transaction.amount);
          }
          return sum;
        }, 0);

        // Calculate refunds
        const totalRefunds = eventTransactions.reduce((sum, transaction) => {
          if (transaction.status === 'REFUNDED' || transaction.status === 'PARTIALLY_REFUNDED') {
            return sum + Number(transaction.refundAmount || 0);
          }
          return sum;
        }, 0);

        // Calculate net revenue
        const netRevenue = totalRevenue - totalRefunds;

        // Calculate tickets sold
        const ticketsSold = event.ticketTypes.reduce((sum, type) => sum + type.quantitySold, 0);

        // Calculate total capacity
        const totalCapacity = event.ticketTypes.reduce((sum, type) => sum + type.quantity, 0);

        // Calculate sell-through rate
        const sellThroughRate = totalCapacity > 0 ? (ticketsSold / totalCapacity) * 100 : 0;

        // Get analytics data
        const analytics = event.analytics || {
          views: 0,
          uniqueVisitors: 0,
          checkoutStarts: 0,
          checkoutCompletions: 0,
        };

        // Calculate conversion rate
        const conversionRate = analytics.uniqueVisitors > 0 
          ? (analytics.checkoutCompletions / analytics.uniqueVisitors) * 100 
          : 0;

        return {
          id: event.id,
          title: event.title,
          startDate: event.startDate,
          endDate: event.endDate,
          category: event.category?.name,
          status: event.status,
          revenue: {
            total: totalRevenue,
            refunds: totalRefunds,
            net: netRevenue,
          },
          tickets: {
            sold: ticketsSold,
            capacity: totalCapacity,
            sellThroughRate,
          },
          traffic: {
            views: analytics.views,
            uniqueVisitors: analytics.uniqueVisitors,
            checkoutStarts: analytics.checkoutStarts,
            checkoutCompletions: analytics.checkoutCompletions,
            conversionRate,
          },
        };
      });

      res.status(200).json({
        success: true,
        data: comparativeData,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Export analytics data
   */
  exportAnalytics: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;
      const { format } = req.query;
      const organizerId = req.user?.id;

      if (!organizerId) {
        throw new ApiError(401, 'Unauthorized');
      }

      // Verify the event belongs to the organizer
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
          organizerId,
        },
        include: {
          ticketTypes: true,
          analytics: true,
          category: true,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get all transactions for this event
      const transactions = await prisma.transaction.findMany({
        where: {
          tickets: {
            some: {
              eventId,
            },
          },
        },
        include: {
          tickets: {
            include: {
              ticketType: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Prepare data for export
      const exportData = {
        eventDetails: {
          id: event.id,
          title: event.title,
          startDate: event.startDate,
          endDate: event.endDate,
          venue: event.venue,
          city: event.city,
          country: event.country,
          category: event.category?.name,
          status: event.status,
        },
        ticketTypes: event.ticketTypes.map(type => ({
          id: type.id,
          name: type.name,
          price: type.price,
          currency: type.currency,
          quantity: type.quantity,
          quantitySold: type.quantitySold,
          salesStartDate: type.salesStartDate,
          salesEndDate: type.salesEndDate,
        })),
        transactions: transactions.map(t => ({
          id: t.id,
          date: t.createdAt,
          customer: t.user ? `${t.user.firstName} ${t.user.lastName}` : 'Anonymous',
          email: t.user?.email,
          amount: t.amount,
          currency: t.currency,
          status: t.status,
          paymentMethod: t.paymentMethod,
          tickets: t.tickets.map(ticket => ({
            id: ticket.id,
            ticketNumber: ticket.ticketNumber,
            ticketType: ticket.ticketType.name,
            price: ticket.ticketType.price,
          })),
        })),
        analytics: event.analytics ? {
          views: event.analytics.views,
          uniqueVisitors: event.analytics.uniqueVisitors,
          checkoutStarts: event.analytics.checkoutStarts,
          checkoutCompletions: event.analytics.checkoutCompletions,
          revenue: event.analytics.revenue,
          refunds: event.analytics.refunds,
          trafficSources: event.analytics.trafficSources,
          dailyStats: event.analytics.dailyStats,
        } : null,
      };

      // Format based on requested format
      if (format === 'csv') {
        // Generate CSV
        const csvData = generateCsvData(exportData);
        
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="analytics_${eventId}.csv"`);
        
        res.status(200).send(csvData);
      } else {
        // Default to JSON
        res.status(200).json({
          success: true,
          data: exportData,
        });
      }
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get attendee demographics
   */
  getAttendeeDemographics: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { eventId } = req.params;
      const organizerId = req.user?.id;

      if (!organizerId) {
        throw new ApiError(401, 'Unauthorized');
      }

      // Verify the event belongs to the organizer
      const event = await prisma.event.findUnique({
        where: {
          id: eventId,
          organizerId,
        },
      });

      if (!event) {
        throw new ApiError(404, 'Event not found');
      }

      // Get all tickets sold for this event with user data
      const tickets = await prisma.ticket.findMany({
        where: {
          eventId,
          status: 'SOLD',
        },
        include: {
          user: true,
        },
      });

      // Extract unique attendees
      const attendeeIds = new Set();
      const attendees = [];

      for (const ticket of tickets) {
        if (ticket.userId && !attendeeIds.has(ticket.userId)) {
          attendeeIds.add(ticket.userId);
          if (ticket.user) {
            attendees.push(ticket.user);
          }
        }
      }

      // Calculate demographics (this would be more comprehensive with real user data)
      // For now, we'll just provide a count and some mock data
      const demographics = {
        totalAttendees: attendees.length,
        newVsReturning: {
          new: Math.floor(attendees.length * 0.7),
          returning: Math.floor(attendees.length * 0.3),
        },
        // Note: In a real implementation, you would calculate these from actual user data
        // These are placeholder values
        ageGroups: {
          'under18': Math.floor(attendees.length * 0.05),
          '18-24': Math.floor(attendees.length * 0.2),
          '25-34': Math.floor(attendees.length * 0.35),
          '35-44': Math.floor(attendees.length * 0.25),
          '45-54': Math.floor(attendees.length * 0.1),
          '55plus': Math.floor(attendees.length * 0.05),
        },
        gender: {
          male: Math.floor(attendees.length * 0.48),
          female: Math.floor(attendees.length * 0.49),
          other: Math.floor(attendees.length * 0.03),
        },
        locations: await getGeographicDistribution(eventId),
      };

      res.status(200).json({
        success: true,
        data: demographics,
      });
    } catch (error) {
      next(error);
    }
  },
};

/**
 * Helper function to get daily sales data
 */
async function getDailySalesData(eventIds: string[], startDate: Date, endDate: Date) {
  // Create a map of dates for the period
  const dailyData: Record<string, { date: string; tickets: number; revenue: number }> = {};
  
  // Initialize with all dates in the range
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    dailyData[dateString] = {
      date: dateString,
      tickets: 0,
      revenue: 0,
    };
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Get ticket sales grouped by date
  const ticketSales = await prisma.ticket.findMany({
    where: {
      eventId: {
        in: eventIds,
      },
      status: 'SOLD',
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      ticketType: true,
      transaction: true,
    },
  });

  // Aggregate sales by date
  ticketSales.forEach(ticket => {
    const dateString = ticket.createdAt.toISOString().split('T')[0];
    
    if (dailyData[dateString]) {
      dailyData[dateString].tickets += 1;
      if (ticket.ticketType) {
        dailyData[dateString].revenue += Number(ticket.ticketType.price);
      }
    }
  });

  // Convert to array and sort by date
  return Object.values(dailyData).sort((a, b) => a.date.localeCompare(b.date));
}

/**
 * Helper function to get top selling events
 */
async function getTopSellingEvents(eventIds: string[], startDate: Date, endDate: Date) {
  // Get all events with their ticket sales
  const events = await prisma.event.findMany({
    where: {
      id: {
        in: eventIds,
      },
    },
    include: {
      tickets: {
        where: {
          status: 'SOLD',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          ticketType: true,
        },
      },
    },
  });

  // Calculate sales and revenue for each event
  const eventSales = events.map(event => {
    const ticketsSold = event.tickets.length;
    const revenue = event.tickets.reduce((sum, ticket) => {
      if (ticket.ticketType) {
        return sum + Number(ticket.ticketType.price);
      }
      return sum;
    }, 0);

    return {
      id: event.id,
      title: event.title,
      startDate: event.startDate,
      endDate: event.endDate,
      ticketsSold,
      revenue,
    };
  });

  // Sort by tickets sold (descending)
  return eventSales.sort((a, b) => b.ticketsSold - a.ticketsSold);
}

/**
 * Helper function to get ticket type distribution
 */
async function getTicketTypeDistribution(eventIds: string[], startDate: Date, endDate: Date) {
  // Get all ticket types for these events
  const ticketTypes = await prisma.ticketType.findMany({
    where: {
      eventId: {
        in: eventIds,
      },
    },
  });

  // Get ticket sales grouped by ticket type
  const ticketSales = await prisma.ticket.groupBy({
    by: ['ticketTypeId'],
    where: {
      eventId: {
        in: eventIds,
      },
      status: 'SOLD',
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    _count: {
      id: true,
    },
  });

  // Map ticket types to their sales
  const distribution = ticketTypes.map(type => {
    const sales = ticketSales.find(sale => sale.ticketTypeId === type.id);
    return {
      id: type.id,
      name: type.name,
      count: sales ? sales._count.id : 0,
    };
  });

  // Calculate percentages
  const totalSold = distribution.reduce((sum, item) => sum + item.count, 0);
  
  return distribution.map(item => ({
    ...item,
    percentage: totalSold > 0 ? (item.count / totalSold) * 100 : 0,
  }));
}

/**
 * Helper function to get traffic sources
 */
async function getTrafficSources(eventIds: string[]) {
  // Get analytics for all events
  const analytics = await prisma.eventAnalytics.findMany({
    where: {
      eventId: {
        in: eventIds,
      },
    },
    select: {
      trafficSources: true,
    },
  });

  // Combine traffic sources from all events
  const combinedSources: Record<string, number> = {
    direct: 0,
    search: 0,
    social: 0,
    email: 0,
    referral: 0,
    other: 0,
  };

  analytics.forEach(item => {
    const sources = item.trafficSources as Record<string, number> || {};
    
    Object.keys(sources).forEach(key => {
      combinedSources[key] = (combinedSources[key] || 0) + (sources[key] || 0);
    });
  });

  // Calculate total and percentages
  const total = Object.values(combinedSources).reduce((sum, count) => sum + count, 0);
  
  const sourcesWithPercentages: Record<string, { count: number; percentage: number }> = {};
  
  Object.keys(combinedSources).forEach(key => {
    sourcesWithPercentages[key] = {
      count: combinedSources[key],
      percentage: total > 0 ? (combinedSources[key] / total) * 100 : 0,
    };
  });

  return sourcesWithPercentages;
}

/**
 * Helper function to get hourly sales distribution
 */
async function getHourlySalesDistribution(eventId: string, startDate: Date, endDate: Date) {
  // Initialize hours array (0-23)
  const hourlyDistribution = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: 0,
    percentage: 0,
  }));

  // Get all tickets sold with their creation time
  const tickets = await prisma.ticket.findMany({
    where: {
      eventId,
      status: 'SOLD',
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    select: {
      createdAt: true,
    },
  });

  // Count tickets by hour
  tickets.forEach(ticket => {
    const hour = ticket.createdAt.getHours();
    hourlyDistribution[hour].count += 1;
  });

  // Calculate percentages
  const totalSold = tickets.length;
  
  hourlyDistribution.forEach(item => {
    item.percentage = totalSold > 0 ? (item.count / totalSold) * 100 : 0;
  });

  return hourlyDistribution;
}

/**
 * Helper function to get geographic distribution of attendees
 */
async function getGeographicDistribution(eventId: string) {
  // Get all tickets with user information
  const tickets = await prisma.ticket.findMany({
    where: {
      eventId,
      status: 'SOLD',
    },
    include: {
      transaction: {
        include: {
          user: true,
        },
      },
    },
    distinct: ['userId'],
  });

  // In a real implementation, you would extract location data from user profiles
  // For now, we'll return a placeholder with mock data
  return {
    countries: {
      'United States': { count: Math.floor(tickets.length * 0.6), percentage: 60 },
      'United Kingdom': { count: Math.floor(tickets.length * 0.15), percentage: 15 },
      'Canada': { count: Math.floor(tickets.length * 0.1), percentage: 10 },
      'Australia': { count: Math.floor(tickets.length * 0.05), percentage: 5 },
      'Other': { count: Math.floor(tickets.length * 0.1), percentage: 10 },
    },
    cities: {
      'New York': { count: Math.floor(tickets.length * 0.3), percentage: 30 },
      'London': { count: Math.floor(tickets.length * 0.15), percentage: 15 },
      'Toronto': { count: Math.floor(tickets.length * 0.1), percentage: 10 },
      'Los Angeles': { count: Math.floor(tickets.length * 0.1), percentage: 10 },
      'Other': { count: Math.floor(tickets.length * 0.35), percentage: 35 },
    },
  };
}

/**
 * Helper function to determine referrer type
 */
function getReferrerType(referrer?: string): string {
  if (!referrer) return 'direct';
  
  const referrerLower = referrer.toLowerCase();
  
  if (referrerLower.includes('google') || referrerLower.includes('bing') || referrerLower.includes('yahoo')) {
    return 'search';
  }
  
  if (
    referrerLower.includes('facebook') || 
    referrerLower.includes('twitter') || 
    referrerLower.includes('instagram') || 
    referrerLower.includes('linkedin') ||
    referrerLower.includes('tiktok')
  ) {
    return 'social';
  }
  
  if (referrerLower.includes('mail') || referrerLower.includes('outlook') || referrerLower.includes('gmail')) {
    return 'email';
  }
  
  return 'referral';
}

/**
 * Helper function to generate CSV data
 */
function generateCsvData(data: any): string {
  // This is a simplified CSV generation
  // In a real implementation, you would use a proper CSV library
  
  // Start with headers
  let csv = 'Event ID,Event Title,Start Date,End Date,Venue,City,Country,Category,Status\n';
  
  // Add event details
  csv += `${data.eventDetails.id},${data.eventDetails.title},${data.eventDetails.startDate},${data.eventDetails.endDate},${data.eventDetails.venue || ''},${data.eventDetails.city || ''},${data.eventDetails.country || ''},${data.eventDetails.category || ''},${data.eventDetails.status}\n\n`;
  
  // Add ticket types
  csv += 'Ticket Type ID,Name,Price,Currency,Quantity,Quantity Sold,Sales Start Date,Sales End Date\n';
  
  data.ticketTypes.forEach((type: any) => {
    csv += `${type.id},${type.name},${type.price},${type.currency},${type.quantity},${type.quantitySold},${type.salesStartDate},${type.salesEndDate}\n`;
  });
  
  csv += '\n';
  
  // Add transactions
  csv += 'Transaction ID,Date,Customer,Email,Amount,Currency,Status,Payment Method\n';
  
  data.transactions.forEach((t: any) => {
    csv += `${t.id},${t.date},${t.customer},${t.email || ''},${t.amount},${t.currency},${t.status},${t.paymentMethod}\n`;
  });
  
  csv += '\n';
  
  // Add analytics if available
  if (data.analytics) {
    csv += 'Views,Unique Visitors,Checkout Starts,Checkout Completions,Revenue,Refunds\n';
    csv += `${data.analytics.views},${data.analytics.uniqueVisitors},${data.analytics.checkoutStarts},${data.analytics.checkoutCompletions},${data.analytics.revenue},${data.analytics.refunds}\n`;
  }
  
  return csv;
}

export default analyticsController;
