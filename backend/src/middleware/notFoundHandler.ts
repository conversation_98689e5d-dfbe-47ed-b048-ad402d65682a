import { Request, Response, NextFunction } from 'express';
import { ApiError } from './errorHandler';

/**
 * Middleware to handle requests to non-existent routes
 * Returns a 404 error with the requested URL in the message
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new ApiError(404, `Resource not found: ${req.originalUrl}`);
  next(error);
};
