import { Request, Response, NextFunction } from 'express';
import { PrismaClientKnownRequestError, PrismaClientValidationError } from '@prisma/client/runtime/library';
import { ZodError } from 'zod';
import logger from '../utils/logger';

// Custom error class for API errors
export class ApiError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(statusCode: number, message: string, isOperational = true, stack = '') {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

// Handle specific Prisma errors
const handlePrismaError = (error: PrismaClientKnownRequestError): ApiError => {
  // P2002: Unique constraint violation
  if (error.code === 'P2002') {
    const field = (error.meta?.target as string[]) || ['field'];
    return new ApiError(409, `Duplicate value for ${field.join(', ')}`);
  }
  
  // P2025: Record not found
  if (error.code === 'P2025') {
    return new ApiError(404, error.message || 'Record not found');
  }
  
  // P2003: Foreign key constraint failed
  if (error.code === 'P2003') {
    return new ApiError(400, 'Related record does not exist');
  }

  // Default Prisma error
  return new ApiError(400, 'Database operation failed');
};

// Handle Zod validation errors
const handleZodError = (error: ZodError): ApiError => {
  const message = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
  return new ApiError(400, `Validation error: ${message}`);
};

// Handle JWT errors
const handleJwtError = (): ApiError => {
  return new ApiError(401, 'Invalid token. Please log in again.');
};

// Handle JWT expired error
const handleJwtExpiredError = (): ApiError => {
  return new ApiError(401, 'Your token has expired. Please log in again.');
};

// Main error handler middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = err;

  // Log the error
  logger.error(`${err.name}: ${err.message}`, { 
    path: req.path,
    method: req.method,
    stack: err.stack,
    body: req.body,
    params: req.params,
    query: req.query,
  });

  // Convert specific errors to ApiError
  if (err instanceof PrismaClientKnownRequestError) {
    error = handlePrismaError(err);
  } else if (err instanceof PrismaClientValidationError) {
    error = new ApiError(400, 'Invalid data provided');
  } else if (err instanceof ZodError) {
    error = handleZodError(err);
  } else if (err.name === 'JsonWebTokenError') {
    error = handleJwtError();
  } else if (err.name === 'TokenExpiredError') {
    error = handleJwtExpiredError();
  } else if (!(err instanceof ApiError)) {
    // For unrecognized errors, create a generic ApiError
    const statusCode = (err as any).statusCode || 500;
    const message = err.message || 'Something went wrong';
    error = new ApiError(statusCode, message, true, err.stack);
  }

  // Cast to ApiError for type safety
  const apiError = error as ApiError;

  // Send error response
  res.status(apiError.statusCode).json({
    success: false,
    error: {
      message: apiError.message,
      code: apiError.statusCode,
      // Only include stack trace in development
      ...(process.env.NODE_ENV === 'development' && { stack: apiError.stack }),
    },
  });
};

// Not found error handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new ApiError(404, `Cannot find ${req.originalUrl} on this server`);
  next(error);
};
