import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { ApiError } from './errorHandler';

/**
 * Middleware to validate request data using express-validator
 * Checks for validation errors and returns a 400 Bad Request response if any are found
 * 
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const validate = (req: Request, res: Response, next: NextFunction): void => {
  // Check for validation errors
  const errors = validationResult(req);
  
  // If there are validation errors
  if (!errors.isEmpty()) {
    // Format the errors
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : undefined,
      message: error.msg,
    }));
    
    // Create an API error with the formatted errors
    const error = new ApiError(
      400, 
      'Validation failed',
      true
    );
    
    // Send the response with validation errors
    res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 400,
        details: formattedErrors,
      },
    });
    return;
  }
  
  // If no validation errors, continue to the next middleware
  next();
};
