import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../index';
import { ApiError } from './errorHandler';
import { UserRole } from '@prisma/client';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
      };
    }
  }
}

// Interface for JWT payload
interface JwtPayload {
  id: string;
  email: string;
  role: UserRole;
}

/**
 * Authentication middleware to protect routes
 * Verifies JWT token from Authorization header or cookies
 */
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header or cookies
    let token: string | undefined;
    
    // Check Authorization header first (Bearer token)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    } 
    // If no token in header, check cookies
    else if (req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }

    // If no token found, throw error
    if (!token) {
      throw new ApiError(401, 'Not authenticated. Please log in.');
    }

    // Verify token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'fallback_secret'
    ) as JwtPayload;

    // Check if user exists in database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        role: true,
        isEmailVerified: true,
      },
    });

    // If user not found or deleted
    if (!user) {
      throw new ApiError(401, 'User no longer exists.');
    }

    // Attach user to request object
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new ApiError(401, 'Invalid token. Please log in again.'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new ApiError(401, 'Token expired. Please log in again.'));
    } else {
      next(error);
    }
  }
};

/**
 * Role-based access control middleware
 * Restricts access to specified roles
 * @param roles - Array of allowed roles
 */
export const restrictTo = (roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new ApiError(401, 'Not authenticated. Please log in.'));
    }

    if (!roles.includes(req.user.role)) {
      return next(
        new ApiError(403, 'You do not have permission to perform this action.')
      );
    }

    next();
  };
};

/**
 * Middleware to check if user is verified
 * Prevents unverified users from accessing certain routes
 */
export const requireVerified = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return next(new ApiError(401, 'Not authenticated. Please log in.'));
  }

  // This would require fetching the user again or including isEmailVerified in the JWT
  prisma.user
    .findUnique({
      where: { id: req.user.id },
      select: { isEmailVerified: true },
    })
    .then((user) => {
      if (!user || !user.isEmailVerified) {
        return next(
          new ApiError(403, 'Please verify your email address to continue.')
        );
      }
      next();
    })
    .catch(next);
};
