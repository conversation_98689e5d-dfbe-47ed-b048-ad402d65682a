import nodemailer from 'nodemailer';
import { createTransport, SendMailOptions, Transporter } from 'nodemailer';
import logger from './logger';

/**
 * Email sending options interface
 */
interface EmailOptions {
  to: string | string[];
  subject: string;
  text: string;
  html?: string;
  from?: string;
  attachments?: {
    filename: string;
    content: Buffer | string;
    contentType?: string;
    path?: string;
    cid?: string;
  }[];
}

/**
 * Create a nodemailer transport based on environment variables
 */
const createEmailTransport = (): Transporter => {
  // For development, use ethereal.email (fake SMTP service)
  if (process.env.NODE_ENV === 'development' && !process.env.SMTP_HOST) {
    logger.info('Using ethereal.email for email testing');
    return nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'ethereal_pass',
      },
    });
  }

  // For production, use configured SMTP server
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_PORT === '465', // true for port 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    // Optional TLS settings
    ...(process.env.NODE_ENV === 'production' && {
      tls: {
        rejectUnauthorized: true,
      },
    }),
  });
};

/**
 * Send an email using the configured transport
 * @param options - Email options including recipient, subject, and content
 * @returns Promise resolving to the sent message info
 */
export const sendEmail = async (options: EmailOptions): Promise<any> => {
  try {
    // Create transport
    const transporter = createEmailTransport();

    // Default from address
    const defaultFrom = process.env.SMTP_FROM || 'TestTicket Toy <<EMAIL>>';

    // Prepare mail options
    const mailOptions: SendMailOptions = {
      from: options.from || defaultFrom,
      to: options.to,
      subject: options.subject,
      text: options.text, // Plain text body
      html: options.html, // HTML body
      attachments: options.attachments,
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);

    // Log success
    logger.info(`Email sent: ${info.messageId}`);

    // For development, log preview URL from ethereal.email
    if (process.env.NODE_ENV === 'development' && info.messageId && (info as any).previewUrl) {
      logger.info(`Preview URL: ${(info as any).previewUrl}`);
    }

    return info;
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Send a batch of emails to multiple recipients
 * @param options - Array of email options
 * @returns Promise resolving to an array of sent message info
 */
export const sendBatchEmails = async (optionsArray: EmailOptions[]): Promise<any[]> => {
  try {
    const results = await Promise.all(optionsArray.map(options => sendEmail(options)));
    logger.info(`Batch email sent to ${optionsArray.length} recipients`);
    return results;
  } catch (error) {
    logger.error('Error sending batch emails:', error);
    throw error;
  }
};

/**
 * Generate a standard email template with the TestTicket Toy branding
 * @param content - HTML content to include in the template
 * @returns HTML string with the content wrapped in the template
 */
export const generateEmailTemplate = (content: string): string => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>TestTicket Toy</title>
      <style>
        body {
          font-family: 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
          background-color: #f9f9f9;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          padding: 20px 0;
          border-bottom: 1px solid #eee;
        }
        .logo {
          max-width: 150px;
          height: auto;
        }
        .content {
          padding: 20px 0;
        }
        .footer {
          text-align: center;
          font-size: 12px;
          color: #888;
          padding: 20px 0;
          border-top: 1px solid #eee;
        }
        .button {
          display: inline-block;
          background-color: #5e72e4;
          color: #ffffff !important;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 4px;
          font-weight: bold;
          margin: 20px 0;
          text-align: center;
        }
        .social-links {
          text-align: center;
          margin-top: 15px;
        }
        .social-link {
          display: inline-block;
          margin: 0 8px;
          color: #5e72e4;
          text-decoration: none;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <img src="https://via.placeholder.com/150x50?text=TestTicket+Toy" alt="TestTicket Toy" class="logo">
        </div>
        <div class="content">
          ${content}
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} TestTicket Toy. All rights reserved.</p>
          <p>If you have any questions, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          <div class="social-links">
            <a href="#" class="social-link">Facebook</a>
            <a href="#" class="social-link">Twitter</a>
            <a href="#" class="social-link">Instagram</a>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Generate a ticket email with QR code and event details
 * @param ticketData - Ticket data including QR code, event details, etc.
 * @returns HTML string for the ticket email
 */
export const generateTicketEmail = (ticketData: {
  eventName: string;
  eventDate: string;
  eventLocation: string;
  ticketType: string;
  ticketNumber: string;
  qrCodeUrl: string;
  attendeeName: string;
  organizerName: string;
}): string => {
  const content = `
    <h1>Your Ticket for ${ticketData.eventName}</h1>
    <p>Hello ${ticketData.attendeeName},</p>
    <p>Thank you for your purchase! Below is your ticket for ${ticketData.eventName}.</p>
    
    <div style="border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; background-color: #f9f9f9;">
      <h2>${ticketData.eventName}</h2>
      <p><strong>Date:</strong> ${ticketData.eventDate}</p>
      <p><strong>Location:</strong> ${ticketData.eventLocation}</p>
      <p><strong>Ticket Type:</strong> ${ticketData.ticketType}</p>
      <p><strong>Ticket Number:</strong> ${ticketData.ticketNumber}</p>
      <div style="text-align: center; margin: 20px 0;">
        <img src="${ticketData.qrCodeUrl}" alt="Ticket QR Code" style="max-width: 200px; height: auto;">
      </div>
      <p style="font-size: 12px; color: #666; text-align: center;">
        Present this QR code at the entrance for quick check-in
      </p>
    </div>
    
    <p>Organized by: ${ticketData.organizerName}</p>
    <p>We look forward to seeing you at the event!</p>
  `;

  return generateEmailTemplate(content);
};
