{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"]}, "types": ["vite/client", "vite-plugin-svgr/client", "vitest/globals", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "build", "coverage"], "references": [{"path": "./tsconfig.node.json"}]}