import { useEffect, useState, lazy, Suspense } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthStore } from './store/authStore';
import { useThemeStore } from './store/themeStore';
import { UserRole } from './types/user';

// Layout components
import MainLayout from './layouts/MainLayout';
import DashboardLayout from './layouts/DashboardLayout';
import AuthLayout from './layouts/AuthLayout';
import LoadingScreen from './components/common/LoadingScreen';
import ProtectedRoute from './components/auth/ProtectedRoute';
import RoleBasedRoute from './components/auth/RoleBasedRoute';

// Lazy-loaded page components
const HomePage = lazy(() => import('./pages/HomePage'));
const EventsPage = lazy(() => import('./pages/EventsPage'));
const EventDetailPage = lazy(() => import('./pages/EventDetailPage'));
const CheckoutPage = lazy(() => import('./pages/CheckoutPage'));
const TicketPage = lazy(() => import('./pages/TicketPage'));

// Auth pages
const LoginPage = lazy(() => import('./pages/auth/LoginPage'));
const RegisterPage = lazy(() => import('./pages/auth/RegisterPage'));
const RegisterOrganizerPage = lazy(() => import('./pages/auth/RegisterOrganizerPage'));
const ForgotPasswordPage = lazy(() => import('./pages/auth/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('./pages/auth/ResetPasswordPage'));
const VerifyEmailPage = lazy(() => import('./pages/auth/VerifyEmailPage'));

// Customer pages
const ProfilePage = lazy(() => import('./pages/customer/ProfilePage'));
const MyTicketsPage = lazy(() => import('./pages/customer/MyTicketsPage'));
const SavedEventsPage = lazy(() => import('./pages/customer/SavedEventsPage'));

// Organizer pages
const OrganizerDashboardPage = lazy(() => import('./pages/organizer/DashboardPage'));
const OrganizerEventsPage = lazy(() => import('./pages/organizer/EventsPage'));
const OrganizerEventCreatePage = lazy(() => import('./pages/organizer/EventCreatePage'));
const OrganizerEventEditPage = lazy(() => import('./pages/organizer/EventEditPage'));
const OrganizerTicketsPage = lazy(() => import('./pages/organizer/TicketsPage'));
const OrganizerAnalyticsPage = lazy(() => import('./pages/organizer/AnalyticsPage'));
const OrganizerPromosPage = lazy(() => import('./pages/organizer/PromosPage'));
const OrganizerMarketingPage = lazy(() => import('./pages/organizer/MarketingPage'));
const OrganizerSettingsPage = lazy(() => import('./pages/organizer/SettingsPage'));
const OrganizerTeamPage = lazy(() => import('./pages/organizer/TeamPage'));

// Marketing & content pages
const AboutPage = lazy(() => import('./pages/marketing/AboutPage'));
const PricingPage = lazy(() => import('./pages/marketing/PricingPage'));
const FeaturesPage = lazy(() => import('./pages/marketing/FeaturesPage'));
const ContactPage = lazy(() => import('./pages/marketing/ContactPage'));
const BlogPage = lazy(() => import('./pages/blog/BlogPage'));
const BlogPostPage = lazy(() => import('./pages/blog/BlogPostPage'));
const FAQPage = lazy(() => import('./pages/support/FAQPage'));
const SupportPage = lazy(() => import('./pages/support/SupportPage'));
const PrivacyPolicyPage = lazy(() => import('./pages/legal/PrivacyPolicyPage'));
const TermsOfServicePage = lazy(() => import('./pages/legal/TermsOfServicePage'));

// Error pages
const NotFoundPage = lazy(() => import('./pages/error/NotFoundPage'));
const ErrorPage = lazy(() => import('./pages/error/ErrorPage'));

const App = () => {
  const { pathname } = useLocation();
  const { checkAuth, isAuthenticated, isLoading, user } = useAuthStore();
  const { theme } = useThemeStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // Check authentication status on app load
  useEffect(() => {
    const initApp = async () => {
      await checkAuth();
      setIsInitialized(true);
    };

    initApp();
  }, [checkAuth]);

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  // Apply theme class to document
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  // Show loading screen while initializing auth
  if (!isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>TestTicket Toy - Event Ticketing Platform</title>
        <meta name="description" content="Discover, create, and manage events with TestTicket Toy. The comprehensive ticketing solution for organizers and attendees." />
      </Helmet>

      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes with main layout */}
          <Route element={<MainLayout />}>
            <Route path="/" element={<HomePage />} />
            <Route path="/events" element={<EventsPage />} />
            <Route path="/events/:eventId" element={<EventDetailPage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/features" element={<FeaturesPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/blog" element={<BlogPage />} />
            <Route path="/blog/:postId" element={<BlogPostPage />} />
            <Route path="/faq" element={<FAQPage />} />
            <Route path="/support" element={<SupportPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/terms-of-service" element={<TermsOfServicePage />} />
          </Route>

          {/* Auth routes */}
          <Route element={<AuthLayout />}>
            <Route path="/login" element={
              isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
            } />
            <Route path="/register" element={
              isAuthenticated ? <Navigate to="/" replace /> : <RegisterPage />
            } />
            <Route path="/register/organizer" element={
              isAuthenticated ? <Navigate to="/" replace /> : <RegisterOrganizerPage />
            } />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password/:token" element={<ResetPasswordPage />} />
            <Route path="/verify-email/:token" element={<VerifyEmailPage />} />
          </Route>

          {/* Protected routes for all authenticated users */}
          <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} isLoading={isLoading} />}>
            <Route element={<MainLayout />}>
              <Route path="/checkout/:eventId" element={<CheckoutPage />} />
              <Route path="/tickets/:ticketId" element={<TicketPage />} />
              
              {/* Customer profile routes */}
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/my-tickets" element={<MyTicketsPage />} />
              <Route path="/saved-events" element={<SavedEventsPage />} />
            </Route>
          </Route>

          {/* Organizer dashboard routes */}
          <Route element={
            <RoleBasedRoute 
              isAuthenticated={isAuthenticated} 
              isLoading={isLoading} 
              userRole={user?.role} 
              allowedRoles={[UserRole.ORGANIZER, UserRole.ADMIN]} 
            />
          }>
            <Route element={<DashboardLayout />}>
              <Route path="/organizer/dashboard" element={<OrganizerDashboardPage />} />
              <Route path="/organizer/events" element={<OrganizerEventsPage />} />
              <Route path="/organizer/events/create" element={<OrganizerEventCreatePage />} />
              <Route path="/organizer/events/:eventId/edit" element={<OrganizerEventEditPage />} />
              <Route path="/organizer/tickets" element={<OrganizerTicketsPage />} />
              <Route path="/organizer/analytics" element={<OrganizerAnalyticsPage />} />
              <Route path="/organizer/promos" element={<OrganizerPromosPage />} />
              <Route path="/organizer/marketing" element={<OrganizerMarketingPage />} />
              <Route path="/organizer/team" element={<OrganizerTeamPage />} />
              <Route path="/organizer/settings" element={<OrganizerSettingsPage />} />
            </Route>
          </Route>

          {/* Admin routes would go here */}

          {/* Error routes */}
          <Route path="/error" element={<ErrorPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </>
  );
};

export default App;
