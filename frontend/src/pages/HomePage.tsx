import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  CalendarIcon, 
  TicketIcon, 
  ChartBarIcon, 
  UserGroupIcon, 
  GlobeAltIcon, 
  CurrencyDollarIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Mock data for featured events
const FEATURED_EVENTS = [
  {
    id: '1',
    title: 'Summer Music Festival 2025',
    description: 'Experience the ultimate summer music festival with top artists from around the world.',
    date: 'Jun 15-18, 2025',
    location: 'Central Park, New York',
    imageUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    price: 'From $49',
    category: 'Music',
  },
  {
    id: '2',
    title: 'Tech Conference 2025',
    description: 'Join industry leaders and innovators at the biggest tech conference of the year.',
    date: 'Jul 10-12, 2025',
    location: 'Convention Center, San Francisco',
    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    price: 'From $199',
    category: 'Conference',
  },
  {
    id: '3',
    title: 'Food & Wine Festival',
    description: 'Taste exquisite dishes and fine wines from renowned chefs and wineries.',
    date: 'Aug 5-7, 2025',
    location: 'Waterfront Park, Chicago',
    imageUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    price: 'From $75',
    category: 'Food & Drink',
  },
  {
    id: '4',
    title: 'International Film Festival',
    description: 'Discover groundbreaking cinema from emerging and established filmmakers worldwide.',
    date: 'Sep 20-30, 2025',
    location: 'Various Theaters, Los Angeles',
    imageUrl: 'https://images.unsplash.com/photo-1478720568477-152d9b164e26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    price: 'From $25',
    category: 'Film & Media',
  },
];

// Mock categories
const CATEGORIES = [
  { id: '1', name: 'Music', icon: '🎵' },
  { id: '2', name: 'Sports', icon: '⚽' },
  { id: '3', name: 'Arts', icon: '🎨' },
  { id: '4', name: 'Food & Drink', icon: '🍷' },
  { id: '5', name: 'Business', icon: '💼' },
  { id: '6', name: 'Technology', icon: '💻' },
  { id: '7', name: 'Workshops', icon: '🔧' },
  { id: '8', name: 'Festivals', icon: '🎪' },
];

// Mock testimonials
const TESTIMONIALS = [
  {
    id: '1',
    quote: "TestTicket Toy transformed how we manage our events. The analytics are incredible, and our ticket sales have increased by 40%!",
    author: "Sarah Johnson",
    role: "Event Organizer, TechCon",
    avatarUrl: "https://randomuser.me/api/portraits/women/12.jpg"
  },
  {
    id: '2',
    quote: "The platform is so easy to use! I found great events in my area and purchasing tickets took less than a minute.",
    author: "Michael Chen",
    role: "Regular Attendee",
    avatarUrl: "https://randomuser.me/api/portraits/men/22.jpg"
  },
  {
    id: '3',
    quote: "As a venue owner, I love how TestTicket Toy has streamlined our ticketing process. The check-in system is flawless.",
    author: "Emma Rodriguez",
    role: "Venue Manager, Skyline Arena",
    avatarUrl: "https://randomuser.me/api/portraits/women/32.jpg"
  },
];

// Mock stats
const STATS = [
  { id: '1', value: '10M+', label: 'Tickets Sold' },
  { id: '2', value: '50K+', label: 'Events Hosted' },
  { id: '3', value: '120+', label: 'Countries' },
  { id: '4', value: '99.9%', label: 'Uptime' },
];

const HomePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Handle scroll to hide/show header on scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 200) {
        setIsHeaderVisible(false);
      } else {
        setIsHeaderVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <>
      <Helmet>
        <title>TestTicket Toy - Discover and Create Amazing Events</title>
        <meta name="description" content="Find and book tickets for the best events or create and manage your own events with TestTicket Toy's comprehensive ticketing platform." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-hero text-white overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{ 
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32 relative z-10">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-shadow-lg">
              Discover, Create, and Experience Amazing Events
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-100">
              Your all-in-one platform for finding tickets or hosting successful events
            </p>
            
            {/* Search bar */}
            <div className="relative max-w-2xl mx-auto mb-8">
              <div className="flex rounded-lg overflow-hidden shadow-lg">
                <input
                  type="text"
                  placeholder="Search events, venues, or cities..."
                  className="w-full px-6 py-4 text-gray-900 focus:outline-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 flex items-center justify-center transition-colors">
                  <MagnifyingGlassIcon className="h-5 w-5" />
                  <span className="ml-2 hidden sm:inline">Search</span>
                </button>
              </div>
            </div>
            
            {/* CTA buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
              <Link to="/events" className="btn-primary btn-lg">
                Find Events
              </Link>
              <Link to="/register/organizer" className="btn-outline bg-white text-primary-700 hover:bg-gray-100 btn-lg">
                Create an Event
              </Link>
            </div>
            
            {/* Trusted by */}
            <div className="mt-12">
              <p className="text-sm font-medium text-gray-300 mb-4">TRUSTED BY LEADING ORGANIZATIONS</p>
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
                <div className="h-8">
                  <svg className="h-full" viewBox="0 0 100 30" fill="currentColor">
                    <rect width="100" height="30" rx="10" />
                  </svg>
                </div>
                <div className="h-8">
                  <svg className="h-full" viewBox="0 0 100 30" fill="currentColor">
                    <circle cx="15" cy="15" r="15" />
                    <rect x="40" width="60" height="30" rx="10" />
                  </svg>
                </div>
                <div className="h-8">
                  <svg className="h-full" viewBox="0 0 100 30" fill="currentColor">
                    <polygon points="50,0 100,30 0,30" />
                  </svg>
                </div>
                <div className="h-8">
                  <svg className="h-full" viewBox="0 0 100 30" fill="currentColor">
                    <rect width="100" height="10" rx="5" />
                    <rect y="20" width="100" height="10" rx="5" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" fill="#ffffff" preserveAspectRatio="none" className="w-full h-[60px]">
            <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
          </svg>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 md:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Discover Events by Category</h2>
            <p className="mt-4 text-xl text-gray-600">Find the perfect events that match your interests</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {CATEGORIES.map((category) => (
              <Link 
                key={category.id} 
                to={`/events?category=${category.name}`}
                className="bg-gray-50 hover:bg-gray-100 rounded-xl p-6 text-center transition-all duration-300 hover:shadow-md group"
              >
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">{category.icon}</div>
                <h3 className="text-lg font-medium text-gray-900">{category.name}</h3>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-10">
            <Link to="/events" className="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
              View all categories
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Events Section */}
      <section className="py-12 md:py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-end mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Featured Events</h2>
              <p className="mt-4 text-xl text-gray-600">Don't miss out on these popular events</p>
            </div>
            <Link to="/events" className="hidden md:inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
              View all events
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {FEATURED_EVENTS.map((event) => (
              <Link key={event.id} to={`/events/${event.id}`} className="event-card group">
                <div className="event-card-image">
                  <img 
                    src={event.imageUrl} 
                    alt={event.title} 
                    className="object-cover w-full h-full"
                  />
                  <div className="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium text-primary-700">
                    {event.category}
                  </div>
                </div>
                <div className="p-5">
                  <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                    {event.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {event.description}
                  </p>
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    {event.date}
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <GlobeAltIcon className="h-4 w-4 mr-1" />
                    {event.location}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-900">{event.price}</span>
                    <span className="text-primary-600 font-medium text-sm">Get tickets</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-10 md:hidden">
            <Link to="/events" className="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
              View all events
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Platform Features Section */}
      <section className="py-12 md:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Why Choose TestTicket Toy?</h2>
            <p className="mt-4 text-xl text-gray-600">The complete platform for event organizers and attendees</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <TicketIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Seamless Ticketing</h3>
              <p className="mt-2 text-gray-600">
                Purchase and manage tickets in seconds with our intuitive platform. Digital tickets delivered instantly.
              </p>
            </div>
            
            {/* Feature 2 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <GlobeAltIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Global Reach</h3>
              <p className="mt-2 text-gray-600">
                Connect with attendees worldwide with multi-currency support and localized experiences.
              </p>
            </div>
            
            {/* Feature 3 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <ChartBarIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Powerful Analytics</h3>
              <p className="mt-2 text-gray-600">
                Gain valuable insights with comprehensive analytics and reporting tools for event organizers.
              </p>
            </div>
            
            {/* Feature 4 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <UserGroupIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Team Collaboration</h3>
              <p className="mt-2 text-gray-600">
                Invite team members with role-based permissions to efficiently manage your events.
              </p>
            </div>
            
            {/* Feature 5 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <CurrencyDollarIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Flexible Pricing</h3>
              <p className="mt-2 text-gray-600">
                Create multiple ticket types, promo codes, and early bird specials to maximize your sales.
              </p>
            </div>
            
            {/* Feature 6 */}
            <div className="feature-card">
              <div className="bg-primary-100 rounded-full p-3 inline-block">
                <CalendarIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="mt-4 text-xl font-bold text-gray-900">Event Discovery</h3>
              <p className="mt-2 text-gray-600">
                Find events that match your interests with our powerful search and recommendation engine.
              </p>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <Link to="/features" className="btn-primary">
              Explore All Features
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-12 md:py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">What Our Users Say</h2>
            <p className="mt-4 text-xl text-gray-600">Join thousands of satisfied organizers and attendees</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {TESTIMONIALS.map((testimonial) => (
              <div key={testimonial.id} className="bg-white p-6 rounded-lg shadow-soft">
                <div className="flex items-center mb-4">
                  <img 
                    src={testimonial.avatarUrl} 
                    alt={testimonial.author}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                  <div className="ml-4">
                    <h4 className="text-lg font-medium text-gray-900">{testimonial.author}</h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">"{testimonial.quote}"</p>
                <div className="mt-4 flex">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 md:py-20 bg-gradient-hero text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold">TestTicket Toy in Numbers</h2>
            <p className="mt-4 text-xl text-gray-100">Trusted by organizers and attendees worldwide</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {STATS.map((stat) => (
              <div key={stat.id} className="text-center">
                <p className="text-4xl md:text-5xl font-bold">{stat.value}</p>
                <p className="mt-2 text-gray-100 font-medium">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-gray-600 mb-8">
            Join TestTicket Toy today and discover the easiest way to create, manage, and attend events.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/register" className="btn-primary btn-lg">
              Sign Up for Free
            </Link>
            <Link to="/contact" className="btn-outline btn-lg">
              Contact Sales
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default HomePage;
