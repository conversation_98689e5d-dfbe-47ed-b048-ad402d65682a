import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  CalendarIcon, 
  MapPinIcon, 
  ClockIcon, 
  UserGroupIcon,
  ShareIcon,
  HeartIcon,
  TicketIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { Event } from '../types/event';
import { useAuthStore } from '../store/authStore';
import LoadingScreen from '../components/common/LoadingScreen';
import toast from 'react-hot-toast';

const EventDetailPage = () => {
  const { eventId } = useParams<{ eventId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        // Mock event data for now
        const mockEvent: Event = {
          id: eventId || '1',
          title: 'Summer Music Festival 2024',
          description: 'Join us for an unforgettable summer music festival featuring top artists from around the world. Experience amazing performances, delicious food, and great vibes in a beautiful outdoor setting.',
          slug: 'summer-music-festival-2024',
          bannerImage: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=800',
          startDate: new Date('2024-07-15T18:00:00'),
          endDate: new Date('2024-07-15T23:00:00'),
          timezone: 'America/New_York',
          venue: {
            name: 'Central Park',
            address: '123 Park Ave, New York, NY 10001',
            city: 'New York',
            state: 'NY',
            country: 'USA',
            coordinates: { lat: 40.7829, lng: -73.9654 }
          },
          isOnline: false,
          category: { id: '1', name: 'Music', slug: 'music' },
          organizer: {
            id: '1',
            name: 'Music Events Co',
            email: '<EMAIL>',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100'
          },
          ticketTypes: [
            {
              id: '1',
              name: 'General Admission',
              description: 'Standard entry to the festival',
              price: 75.00,
              currency: 'USD',
              quantity: 1000,
              sold: 650,
              maxPerOrder: 8,
              salesStart: new Date('2024-01-01'),
              salesEnd: new Date('2024-07-15T12:00:00'),
              isActive: true
            },
            {
              id: '2',
              name: 'VIP Pass',
              description: 'VIP access with premium amenities',
              price: 150.00,
              currency: 'USD',
              quantity: 200,
              sold: 120,
              maxPerOrder: 4,
              salesStart: new Date('2024-01-01'),
              salesEnd: new Date('2024-07-15T12:00:00'),
              isActive: true
            }
          ],
          status: 'published',
          isPublic: true,
          isFeatured: true,
          tags: ['music', 'festival', 'outdoor', 'summer'],
          totalCapacity: 1200,
          soldTickets: 770,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15')
        };

        setEvent(mockEvent);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching event:', error);
        toast.error('Failed to load event details');
        setLoading(false);
      }
    };

    if (eventId) {
      fetchEvent();
    }
  }, [eventId]);

  const handleSaveEvent = () => {
    if (!isAuthenticated) {
      toast.error('Please log in to save events');
      navigate('/login');
      return;
    }
    
    setIsSaved(!isSaved);
    toast.success(isSaved ? 'Event removed from saved' : 'Event saved!');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: event?.title,
        text: event?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const handleBuyTickets = () => {
    if (!isAuthenticated) {
      toast.error('Please log in to purchase tickets');
      navigate('/login');
      return;
    }
    
    navigate(`/checkout/${eventId}`);
  };

  if (loading) {
    return <LoadingScreen message="Loading event details..." />;
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Event Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">The event you're looking for doesn't exist.</p>
          <Link to="/events" className="btn-primary">
            Browse Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{event.title} - TestTicket Toy</title>
        <meta name="description" content={event.description} />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <div className="relative h-96 bg-gray-900">
          <img
            src={event.bannerImage}
            alt={event.title}
            className="w-full h-full object-cover opacity-70"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          
          <div className="absolute bottom-0 left-0 right-0 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-end justify-between">
                <div className="text-white">
                  <h1 className="text-4xl font-bold mb-2">{event.title}</h1>
                  <p className="text-xl opacity-90">{event.organizer.name}</p>
                </div>
                
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleSaveEvent}
                    className="p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
                  >
                    {isSaved ? (
                      <HeartSolidIcon className="h-6 w-6 text-red-500" />
                    ) : (
                      <HeartIcon className="h-6 w-6 text-white" />
                    )}
                  </button>
                  
                  <button
                    onClick={handleShare}
                    className="p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
                  >
                    <ShareIcon className="h-6 w-6 text-white" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Event Info */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="flex items-center">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {event.startDate.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {event.startDate.toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit'
                        })} - {event.endDate.toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <MapPinIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{event.venue.name}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{event.venue.address}</p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">About This Event</h2>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{event.description}</p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Ticket Selection */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 sticky top-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Select Tickets</h3>
                
                <div className="space-y-4 mb-6">
                  {event.ticketTypes.map((ticket) => (
                    <div key={ticket.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">{ticket.name}</h4>
                        <span className="text-lg font-bold text-primary-600">${ticket.price}</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{ticket.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {ticket.quantity - ticket.sold} remaining
                        </span>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-primary-600 h-2 rounded-full" 
                            style={{ width: `${(ticket.sold / ticket.quantity) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <button
                  onClick={handleBuyTickets}
                  className="w-full btn-primary flex items-center justify-center"
                >
                  <TicketIcon className="h-5 w-5 mr-2" />
                  Get Tickets
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventDetailPage;
