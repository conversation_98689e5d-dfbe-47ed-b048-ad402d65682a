import { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  MagnifyingGlassIcon, 
  CalendarIcon, 
  MapPinIcon, 
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  ListBulletIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline';
import { Disclosure, Transition, Popover } from '@headlessui/react';
import { EventSummary, Category } from '../types/event';
import { api } from '../services/api';
import toast from 'react-hot-toast';
import LoadingScreen from '../components/common/LoadingScreen';

// Mock categories until we fetch from API
const MOCK_CATEGORIES: Category[] = [
  { id: '1', name: 'Music', slug: 'music', icon: '🎵', color: '#3b82f6', createdAt: '', updatedAt: '' },
  { id: '2', name: 'Sports', slug: 'sports', icon: '⚽', color: '#22c55e', createdAt: '', updatedAt: '' },
  { id: '3', name: 'Arts', slug: 'arts', icon: '🎨', color: '#f59e0b', createdAt: '', updatedAt: '' },
  { id: '4', name: 'Food & Drink', slug: 'food-drink', icon: '🍷', color: '#ef4444', createdAt: '', updatedAt: '' },
  { id: '5', name: 'Business', slug: 'business', icon: '💼', color: '#6366f1', createdAt: '', updatedAt: '' },
  { id: '6', name: 'Technology', slug: 'technology', icon: '💻', color: '#8b5cf6', createdAt: '', updatedAt: '' },
  { id: '7', name: 'Workshops', slug: 'workshops', icon: '🔧', color: '#64748b', createdAt: '', updatedAt: '' },
  { id: '8', name: 'Festivals', slug: 'festivals', icon: '🎪', color: '#ec4899', createdAt: '', updatedAt: '' },
];

// Mock locations until we fetch from API
const MOCK_LOCATIONS = [
  'New York, NY',
  'Los Angeles, CA',
  'Chicago, IL',
  'Houston, TX',
  'Phoenix, AZ',
  'Philadelphia, PA',
  'San Antonio, TX',
  'San Diego, CA',
  'Dallas, TX',
  'San Jose, CA',
];

// Sort options
const SORT_OPTIONS = [
  { value: 'date', label: 'Date (Soonest)' },
  { value: 'date-desc', label: 'Date (Latest)' },
  { value: 'price', label: 'Price (Low to High)' },
  { value: 'price-desc', label: 'Price (High to Low)' },
  { value: 'name', label: 'Name (A-Z)' },
  { value: 'name-desc', label: 'Name (Z-A)' },
];

// Date filter options
const DATE_FILTER_OPTIONS = [
  { value: 'today', label: 'Today' },
  { value: 'tomorrow', label: 'Tomorrow' },
  { value: 'this-week', label: 'This Week' },
  { value: 'this-weekend', label: 'This Weekend' },
  { value: 'next-week', label: 'Next Week' },
  { value: 'next-month', label: 'Next Month' },
  { value: 'custom', label: 'Custom Date Range' },
];

const EventsPage = () => {
  // URL search params
  const [searchParams, setSearchParams] = useSearchParams();
  
  // State variables
  const [loading, setLoading] = useState(true);
  const [events, setEvents] = useState<EventSummary[]>([]);
  const [categories, setCategories] = useState<Category[]>(MOCK_CATEGORIES);
  const [locations, setLocations] = useState<string[]>(MOCK_LOCATIONS);
  const [totalEvents, setTotalEvents] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    searchParams.get('categories')?.split(',') || []
  );
  const [selectedLocation, setSelectedLocation] = useState(searchParams.get('location') || '');
  const [dateFilter, setDateFilter] = useState(searchParams.get('date') || '');
  const [priceRange, setPriceRange] = useState({
    min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice') || '0') : 0,
    max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice') || '1000') : 1000,
  });
  const [isOnlineOnly, setIsOnlineOnly] = useState(searchParams.get('online') === 'true');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'date');
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(
    searchParams.get('page') ? parseInt(searchParams.get('page') || '1') : 1
  );
  const [eventsPerPage] = useState(12);
  
  // Format date for display
  const formatEventDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const startMonth = start.toLocaleString('default', { month: 'short' });
    const endMonth = end.toLocaleString('default', { month: 'short' });
    
    if (startMonth === endMonth && start.getDate() === end.getDate() && start.getFullYear() === end.getFullYear()) {
      // Same day event
      return `${startMonth} ${start.getDate()}, ${start.getFullYear()}`;
    } else if (start.getFullYear() === end.getFullYear()) {
      // Same year
      if (startMonth === endMonth) {
        // Same month
        return `${startMonth} ${start.getDate()}-${end.getDate()}, ${start.getFullYear()}`;
      } else {
        // Different months
        return `${startMonth} ${start.getDate()} - ${endMonth} ${end.getDate()}, ${start.getFullYear()}`;
      }
    } else {
      // Different years
      return `${startMonth} ${start.getDate()}, ${start.getFullYear()} - ${endMonth} ${end.getDate()}, ${end.getFullYear()}`;
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };
  
  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateSearchParams({ q: searchQuery, page: '1' });
  };
  
  // Handle category selection
  const handleCategoryChange = (categoryId: string) => {
    let newCategories: string[];
    
    if (selectedCategories.includes(categoryId)) {
      // Remove category if already selected
      newCategories = selectedCategories.filter(id => id !== categoryId);
    } else {
      // Add category if not selected
      newCategories = [...selectedCategories, categoryId];
    }
    
    setSelectedCategories(newCategories);
    updateSearchParams({ 
      categories: newCategories.length > 0 ? newCategories.join(',') : null,
      page: '1'
    });
  };
  
  // Handle location selection
  const handleLocationChange = (location: string) => {
    setSelectedLocation(location);
    updateSearchParams({ location, page: '1' });
  };
  
  // Handle date filter change
  const handleDateFilterChange = (filter: string) => {
    setDateFilter(filter);
    updateSearchParams({ date: filter, page: '1' });
  };
  
  // Handle price range change
  const handlePriceRangeChange = (min: number, max: number) => {
    setPriceRange({ min, max });
    updateSearchParams({ minPrice: min.toString(), maxPrice: max.toString(), page: '1' });
  };
  
  // Handle online only toggle
  const handleOnlineOnlyChange = (checked: boolean) => {
    setIsOnlineOnly(checked);
    updateSearchParams({ online: checked.toString(), page: '1' });
  };
  
  // Handle sort change
  const handleSortChange = (sort: string) => {
    setSortBy(sort);
    updateSearchParams({ sort, page: '1' });
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateSearchParams({ page: page.toString() });
  };
  
  // Handle view mode change
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    // We don't update URL params for this as it's just a UI preference
  };
  
  // Update search params in URL
  const updateSearchParams = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    
    // Update or remove each param
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === '') {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });
    
    setSearchParams(newSearchParams);
  };
  
  // Clear all filters
  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedCategories([]);
    setSelectedLocation('');
    setDateFilter('');
    setPriceRange({ min: 0, max: 1000 });
    setIsOnlineOnly(false);
    setSortBy('date');
    setCurrentPage(1);
    setSearchParams(new URLSearchParams());
  };
  
  // Fetch events based on filters
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      
      try {
        // In a real implementation, we would call the API with all filters
        // For now, we'll simulate a delay and return mock data
        const params = {
          page: currentPage.toString(),
          limit: eventsPerPage.toString(),
          q: searchQuery,
          categories: selectedCategories.join(','),
          location: selectedLocation,
          date: dateFilter,
          minPrice: priceRange.min.toString(),
          maxPrice: priceRange.max.toString(),
          online: isOnlineOnly.toString(),
          sort: sortBy,
        };
        
        // Simulate API call
        // const response = await api.get('/api/v1/events', { params });
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Generate mock events based on filters
        const mockEvents: EventSummary[] = Array.from({ length: eventsPerPage }, (_, i) => {
          const isOnline = isOnlineOnly || Math.random() > 0.7;
          const categoryIndex = Math.floor(Math.random() * categories.length);
          const startDate = new Date();
          startDate.setDate(startDate.getDate() + Math.floor(Math.random() * 60));
          const endDate = new Date(startDate);
          endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 3));
          
          const price = Math.floor(Math.random() * (priceRange.max - priceRange.min + 1)) + priceRange.min;
          
          return {
            id: `event-${currentPage}-${i}`,
            title: `Event ${currentPage * eventsPerPage + i + 1}`,
            slug: `event-${currentPage}-${i}`,
            shortDescription: 'This is a sample event description that gives you an idea of what this event is about.',
            bannerImage: `https://source.unsplash.com/random/800x600?sig=${currentPage * eventsPerPage + i}`,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            venue: isOnline ? undefined : `Venue ${i + 1}`,
            city: isOnline ? undefined : MOCK_LOCATIONS[Math.floor(Math.random() * MOCK_LOCATIONS.length)].split(',')[0],
            country: isOnline ? undefined : 'United States',
            isOnline,
            status: 'PUBLISHED',
            category: categories[categoryIndex],
            lowestPrice: price,
            currency: 'USD',
            ticketsSold: Math.floor(Math.random() * 500),
            totalCapacity: 1000,
          };
        });
        
        setEvents(mockEvents);
        setTotalEvents(120); // Mock total for pagination
        setLoading(false);
      } catch (error) {
        console.error('Error fetching events:', error);
        toast.error('Failed to load events');
        setLoading(false);
      }
    };
    
    fetchEvents();
  }, [
    searchQuery, 
    selectedCategories, 
    selectedLocation, 
    dateFilter, 
    priceRange, 
    isOnlineOnly, 
    sortBy, 
    currentPage, 
    eventsPerPage,
    categories
  ]);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // In a real implementation, we would fetch categories from API
        // For now, we'll use mock data
        // const response = await api.get('/api/v1/categories');
        // setCategories(response.data.data.categories);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalEvents / eventsPerPage);
  
  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // Show all pages if total pages are less than or equal to maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);
      
      // Calculate start and end page numbers
      let startPage = Math.max(2, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = Math.min(totalPages - 1, startPage + maxPagesToShow - 3);
      
      if (endPage - startPage < maxPagesToShow - 3) {
        startPage = Math.max(2, endPage - (maxPagesToShow - 3));
      }
      
      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pageNumbers.push('...');
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
      
      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pageNumbers.push('...');
      }
      
      // Always include last page
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };
  
  // Count active filters
  const activeFiltersCount = [
    searchQuery ? 1 : 0,
    selectedCategories.length > 0 ? 1 : 0,
    selectedLocation ? 1 : 0,
    dateFilter ? 1 : 0,
    (priceRange.min > 0 || priceRange.max < 1000) ? 1 : 0,
    isOnlineOnly ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  return (
    <>
      <Helmet>
        <title>Browse Events - TestTicket Toy</title>
        <meta name="description" content="Discover and book tickets for upcoming events near you. Find concerts, festivals, conferences, workshops, and more." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-12 md:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Find Your Next Experience</h1>
            <p className="text-lg md:text-xl mb-6">Discover amazing events happening around you</p>
            
            {/* Search form */}
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className="flex rounded-lg overflow-hidden shadow-lg">
                <input
                  type="text"
                  placeholder="Search events, venues, or cities..."
                  className="w-full px-6 py-4 text-gray-900 focus:outline-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button 
                  type="submit" 
                  className="bg-primary-700 hover:bg-primary-800 text-white px-6 flex items-center justify-center transition-colors"
                >
                  <MagnifyingGlassIcon className="h-5 w-5" />
                  <span className="ml-2 hidden sm:inline">Search</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-8 md:py-12 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters - Desktop */}
            <div className="hidden lg:block w-64 flex-shrink-0">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 sticky top-24">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">Filters</h2>
                  {activeFiltersCount > 0 && (
                    <button
                      onClick={clearAllFilters}
                      className="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300"
                    >
                      Clear all
                    </button>
                  )}
                </div>

                {/* Categories */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Categories</h3>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center">
                        <input
                          id={`category-${category.id}`}
                          name={`category-${category.id}`}
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          checked={selectedCategories.includes(category.id)}
                          onChange={() => handleCategoryChange(category.id)}
                        />
                        <label
                          htmlFor={`category-${category.id}`}
                          className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                        >
                          {category.icon} {category.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Location */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Location</h3>
                  <select
                    id="location"
                    name="location"
                    className="form-control"
                    value={selectedLocation}
                    onChange={(e) => handleLocationChange(e.target.value)}
                  >
                    <option value="">Any Location</option>
                    {locations.map((location) => (
                      <option key={location} value={location}>
                        {location}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Date */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Date</h3>
                  <select
                    id="date"
                    name="date"
                    className="form-control"
                    value={dateFilter}
                    onChange={(e) => handleDateFilterChange(e.target.value)}
                  >
                    <option value="">Any Date</option>
                    {DATE_FILTER_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Price Range */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Price Range: {formatCurrency(priceRange.min)} - {formatCurrency(priceRange.max)}
                  </h3>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="0"
                      max="1000"
                      step="10"
                      value={priceRange.min}
                      onChange={(e) => handlePriceRangeChange(parseInt(e.target.value), priceRange.max)}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                  <div className="flex items-center space-x-4 mt-2">
                    <input
                      type="range"
                      min="0"
                      max="1000"
                      step="10"
                      value={priceRange.max}
                      onChange={(e) => handlePriceRangeChange(priceRange.min, parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                </div>

                {/* Online Only */}
                <div className="mb-6">
                  <div className="flex items-center">
                    <input
                      id="online-only"
                      name="online-only"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      checked={isOnlineOnly}
                      onChange={(e) => handleOnlineOnlyChange(e.target.checked)}
                    />
                    <label
                      htmlFor="online-only"
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      Online events only
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Events Grid */}
            <div className="flex-1">
              {/* Toolbar */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6 flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center">
                  {/* Mobile filter button */}
                  <button
                    type="button"
                    className="lg:hidden inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
                    onClick={() => setMobileFiltersOpen(true)}
                  >
                    <FunnelIcon className="h-4 w-4 mr-1" />
                    Filters
                    {activeFiltersCount > 0 && (
                      <span className="ml-1 bg-primary-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                        {activeFiltersCount}
                      </span>
                    )}
                  </button>

                  <div className="ml-4 text-sm text-gray-500 dark:text-gray-400">
                    {loading ? (
                      'Loading events...'
                    ) : (
                      <>
                        Showing <span className="font-medium text-gray-900 dark:text-white">{events.length}</span> of{' '}
                        <span className="font-medium text-gray-900 dark:text-white">{totalEvents}</span> events
                      </>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Sort dropdown */}
                  <div className="flex items-center">
                    <ArrowsUpDownIcon className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-1" />
                    <select
                      id="sort"
                      name="sort"
                      className="form-control py-1 px-2 text-sm"
                      value={sortBy}
                      onChange={(e) => handleSortChange(e.target.value)}
                    >
                      {SORT_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* View mode toggle */}
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      className={`p-1.5 rounded-md ${
                        viewMode === 'grid'
                          ? 'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-400'
                          : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => handleViewModeChange('grid')}
                      aria-label="Grid view"
                    >
                      <Squares2X2Icon className="h-5 w-5" />
                    </button>
                    <button
                      type="button"
                      className={`p-1.5 rounded-md ${
                        viewMode === 'list'
                          ? 'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-400'
                          : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => handleViewModeChange('list')}
                      aria-label="List view"
                    >
                      <ListBulletIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Events */}
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <LoadingScreen message="Loading events..." fullScreen={false} transparent={true} />
                </div>
              ) : events.length > 0 ? (
                <>
                  {viewMode === 'grid' ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {events.map((event) => (
                        <Link
                          key={event.id}
                          to={`/events/${event.slug}`}
                          className="event-card group"
                        >
                          <div className="event-card-image">
                            <img
                              src={event.bannerImage}
                              alt={event.title}
                              className="object-cover w-full h-full"
                            />
                            <div className="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium text-primary-700">
                              {event.category?.name}
                            </div>
                            {event.isOnline && (
                              <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                                Online Event
                              </div>
                            )}
                          </div>
                          <div className="p-5">
                            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 transition-colors line-clamp-1">
                              {event.title}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                              {event.shortDescription}
                            </p>
                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              {formatEventDate(event.startDate, event.endDate)}
                            </div>
                            {!event.isOnline && event.city && (
                              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                                <MapPinIcon className="h-4 w-4 mr-1" />
                                {event.city}, {event.country}
                              </div>
                            )}
                            <div className="flex justify-between items-center">
                              <span className="font-medium text-gray-900 dark:text-white">
                                From {formatCurrency(event.lowestPrice, event.currency)}
                              </span>
                              <span className="text-primary-600 dark:text-primary-400 font-medium text-sm">
                                Get tickets
                              </span>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {events.map((event) => (
                        <Link
                          key={event.id}
                          to={`/events/${event.slug}`}
                          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden flex flex-col sm:flex-row hover:shadow-md transition-shadow"
                        >
                          <div className="sm:w-48 md:w-64 h-48 sm:h-auto relative">
                            <img
                              src={event.bannerImage}
                              alt={event.title}
                              className="object-cover w-full h-full"
                            />
                            {event.isOnline && (
                              <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                                Online Event
                              </div>
                            )}
                          </div>
                          <div className="p-5 flex-1 flex flex-col">
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 hover:text-primary-600 transition-colors">
                                  {event.title}
                                </h3>
                                <span className="bg-white px-3 py-1 rounded-full text-sm font-medium text-primary-700 border border-primary-100">
                                  {event.category?.name}
                                </span>
                              </div>
                              <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                                {event.shortDescription}
                              </p>
                            </div>
                            <div className="flex flex-wrap items-end justify-between mt-2">
                              <div className="space-y-1">
                                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                  <CalendarIcon className="h-4 w-4 mr-1" />
                                  {formatEventDate(event.startDate, event.endDate)}
                                </div>
                                {!event.isOnline && event.city && (
                                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <MapPinIcon className="h-4 w-4 mr-1" />
                                    {event.city}, {event.country}
                                  </div>
                                )}
                              </div>
                              <div className="mt-2 sm:mt-0 flex items-center">
                                <span className="font-medium text-gray-900 dark:text-white mr-3">
                                  From {formatCurrency(event.lowestPrice, event.currency)}
                                </span>
                                <span className="text-primary-600 dark:text-primary-400 font-medium text-sm whitespace-nowrap">
                                  Get tickets →
                                </span>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex items-center justify-center mt-8">
                      <nav className="flex items-center space-x-2" aria-label="Pagination">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`px-3 py-2 rounded-md text-sm font-medium ${
                            currentPage === 1
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                          }`}
                        >
                          Previous
                        </button>
                        
                        {getPageNumbers().map((page, index) => (
                          <button
                            key={index}
                            onClick={() => typeof page === 'number' && handlePageChange(page)}
                            disabled={page === '...'}
                            className={`px-3 py-2 rounded-md text-sm font-medium ${
                              page === currentPage
                                ? 'bg-primary-600 text-white'
                                : page === '...'
                                ? 'text-gray-400'
                                : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                            }`}
                          >
                            {page}
                          </button>
                        ))}
                        
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`px-3 py-2 rounded-md text-sm font-medium ${
                            currentPage === totalPages
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                          }`}
                        >
                          Next
                        </button>
                      </nav>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                    <CalendarIcon className="h-12 w-12 text-gray-400 dark:text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No events found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6">
                    Try adjusting your search or filter criteria
                  </p>
                  <button
                    onClick={clearAllFilters}
                    className="btn-primary"
                  >
                    Clear all filters
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Filters Sidebar */}
      <Transition show={mobileFiltersOpen} as="div" className="lg:hidden">
        <div className="fixed inset-0 flex z-40">
          {/* Backdrop */}
          <Transition.Child
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div 
              className="fixed inset-0 bg-black bg-opacity-25"
              onClick={() => setMobileFiltersOpen(false)}
            />
          </Transition.Child>

          {/* Sidebar */}
          <Transition.Child
            enter="transition ease-in-out duration-300 transform"
            enterFrom="translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="translate-x-full"
            className="ml-auto relative max-w-xs w-full h-full bg-white dark:bg-gray-800 shadow-xl py-4 pb-12 flex flex-col overflow-y-auto"
          >
            <div className="px-4 flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Filters</h2>
              <button
                type="button"
                className="p-2 -mr-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200"
                onClick={() => setMobileFiltersOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>

            {/* Filters */}
            <div className="mt-4 border-t border-gray-200 dark:border-gray-700 px-4 py-6">
              {/* Categories */}
              <Disclosure defaultOpen={true} as="div" className="mb-6">
                {({ open }) => (
                  <>
                    <Disclosure.Button className="flex w-full justify-between items-center text-left">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Categories</h3>
                      <span className="ml-6 flex items-center">
                        {open ? (
                          <MinusIcon className="h-4 w-4" />
                        ) : (
                          <PlusIcon className="h-4 w-4" />
                        )}
                      </span>
                    </Disclosure.Button>
                    <Disclosure.Panel className="mt-4 space-y-2">
                      {categories.map((category) => (
                        <div key={category.id} className="flex items-center">
                          <input
                            id={`mobile-category-${category.id}`}
                            name={`mobile-category-${category.id}`}
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            checked={selectedCategories.includes(category.id)}
                            onChange={() => handleCategoryChange(category.id)}
                          />
                          <label
                            htmlFor={`mobile-category-${category.id}`}
                            className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                          >
                            {category.icon} {category.name}
                          </label>
                        </div>
                      ))}
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>

              {/* Location */}
              <Disclosure defaultOpen={true} as="div" className="mb-6">
                {({ open }) => (
                  <>
                    <Disclosure.Button className="flex w-full justify-between items-center text-left">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Location</h3>
                      <span className="ml-6 flex items-center">
                        {open ? (
                          <MinusIcon className="h-4 w-4" />
                        ) : (
                          <PlusIcon className="h-4 w-4" />
                        )}
                      </span>
                    </Disclosure.Button>
                    <Disclosure.Panel className="mt-4">
                      <select
                        id="mobile-location"
                        name="mobile-location"
                        className="form-control"
                        value={selectedLocation}
                        onChange={(e) => handleLocationChange(e.target.value)}
                      >
                        <option value="">Any Location</option>
                        {locations.map((location) => (
                          <option key={location} value={location}>
                            {location}
                          </option>
                        ))}
                      </select>
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>

              {/* Date */}
              <Disclosure defaultOpen={true} as="div" className="mb-6">
                {({ open }) => (
                  <>
                    <Disclosure.Button className="flex w-full justify-between items-center text-left">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Date</h3>
                      <span className="ml-6 flex items-center">
                        {open ? (
                          <MinusIcon className="h-4 w-4" />
                        ) : (
                          <PlusIcon className="h-4 w-4" />
                        )}
                      </span>
                    </Disclosure.Button>
                    <Disclosure.Panel className="mt-4">
                      <select
                        id="mobile-date"
                        name="mobile-date"
                        className="form-control"
                        value={dateFilter}
                        onChange={(e) => handleDateFilterChange(e.target.value)}
                      >
                        <option value="">Any Date</option>
                        {DATE_FILTER_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>

              {/* Price Range */}
              <Disclosure defaultOpen={true} as="div" className="mb-6">
                {({ open }) => (
                  <>
                    <Disclosure.Button className="flex w-full justify-between items-center text-left">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Price Range</h3>
                      <span className="ml-6 flex items-center">
                        {open ? (
                          <MinusIcon className="h-4 w-4" />
                        ) : (
                          <PlusIcon className="h-4 w-4" />
                        )}
                      </span>
                    </Disclosure.Button>
                    <Disclosure.Panel className="mt-4">
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        {formatCurrency(priceRange.min)} - {formatCurrency(priceRange.max)}
                      </p>
                      <div className="flex items-center space-x-4">
                        <input
                          type="range"
                          min="0"
                          max="1000"
                          step="10"
                          value={priceRange.min}
                          onChange={(e) => handlePriceRangeChange(parseInt(e.target.value), priceRange.max)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                        />
                      </div>
                      <div className="flex items-center space-x-4 mt-2">
                        <input
                          type="range"
                          min="0"
                          max="1000"
                          step="10"
                          value={priceRange.max}
                          onChange={(e) => handlePriceRangeChange(priceRange.min, parseInt(e.target.value))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                        />
                      </div>
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>

              {/* Online Only */}
              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    id="mobile-online-only"
                    name="mobile-online-only"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    checked={isOnlineOnly}
                    onChange={(e) => handleOnlineOnlyChange(e.target.checked)}
                  />
                  <label
                    htmlFor="mobile-online-only"
                    className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                  >
                    Online events only
                  </label>
                </div>
              </div>

              {/* Apply button */}
              <div className="mt-6">
                <button
                  type="button"
                  className="btn-primary w-full"
                  onClick={() => setMobileFiltersOpen(false)}
                >
                  Apply Filters
                </button>
                {activeFiltersCount > 0 && (
                  <button
                    type="button"
                    className="mt-4 w-full px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
                    onClick={clearAllFilters}
                  >
                    Clear All Filters
                  </button>
                )}
              </div>
            </div>
          </Transition.Child>
        </div>
      </Transition>
    </>
  );
};

// Helper icons
const PlusIcon = ({ className = 'h-5 w-5' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
  </svg>
);

const MinusIcon = ({ className = 'h-5 w-5' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
  </svg>
);

export default EventsPage;
