import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { 
  CreditCardIcon, 
  LockClosedIcon,
  CalendarIcon,
  MapPinIcon,
  TicketIcon,
  MinusIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { Event, TicketType } from '../types/event';
import { useAuthStore } from '../store/authStore';
import LoadingScreen from '../components/common/LoadingScreen';
import toast from 'react-hot-toast';

interface TicketSelection {
  ticketTypeId: string;
  quantity: number;
}

interface CheckoutFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZip: string;
}

const CheckoutSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string().required('Phone number is required'),
  cardNumber: Yup.string()
    .matches(/^\d{16}$/, 'Card number must be 16 digits')
    .required('Card number is required'),
  expiryDate: Yup.string()
    .matches(/^\d{2}\/\d{2}$/, 'Expiry date must be MM/YY format')
    .required('Expiry date is required'),
  cvv: Yup.string()
    .matches(/^\d{3,4}$/, 'CVV must be 3 or 4 digits')
    .required('CVV is required'),
  billingAddress: Yup.string().required('Billing address is required'),
  billingCity: Yup.string().required('City is required'),
  billingState: Yup.string().required('State is required'),
  billingZip: Yup.string().required('ZIP code is required'),
});

const CheckoutPage = () => {
  const { eventId } = useParams<{ eventId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [ticketSelections, setTicketSelections] = useState<TicketSelection[]>([]);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        // Mock event data
        const mockEvent: Event = {
          id: eventId || '1',
          title: 'Summer Music Festival 2024',
          description: 'Join us for an unforgettable summer music festival.',
          slug: 'summer-music-festival-2024',
          bannerImage: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=400',
          startDate: new Date('2024-07-15T18:00:00'),
          endDate: new Date('2024-07-15T23:00:00'),
          timezone: 'America/New_York',
          venue: {
            name: 'Central Park',
            address: '123 Park Ave, New York, NY 10001',
            city: 'New York',
            state: 'NY',
            country: 'USA',
            coordinates: { lat: 40.7829, lng: -73.9654 }
          },
          isOnline: false,
          category: { id: '1', name: 'Music', slug: 'music' },
          organizer: {
            id: '1',
            name: 'Music Events Co',
            email: '<EMAIL>',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100'
          },
          ticketTypes: [
            {
              id: '1',
              name: 'General Admission',
              description: 'Standard entry to the festival',
              price: 75.00,
              currency: 'USD',
              quantity: 1000,
              sold: 650,
              maxPerOrder: 8,
              salesStart: new Date('2024-01-01'),
              salesEnd: new Date('2024-07-15T12:00:00'),
              isActive: true
            },
            {
              id: '2',
              name: 'VIP Pass',
              description: 'VIP access with premium amenities',
              price: 150.00,
              currency: 'USD',
              quantity: 200,
              sold: 120,
              maxPerOrder: 4,
              salesStart: new Date('2024-01-01'),
              salesEnd: new Date('2024-07-15T12:00:00'),
              isActive: true
            }
          ],
          status: 'published',
          isPublic: true,
          isFeatured: true,
          tags: ['music', 'festival', 'outdoor', 'summer'],
          totalCapacity: 1200,
          soldTickets: 770,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15')
        };

        setEvent(mockEvent);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching event:', error);
        toast.error('Failed to load event details');
        setLoading(false);
      }
    };

    if (eventId) {
      fetchEvent();
    }
  }, [eventId]);

  const updateTicketQuantity = (ticketTypeId: string, quantity: number) => {
    setTicketSelections(prev => {
      const existing = prev.find(t => t.ticketTypeId === ticketTypeId);
      if (existing) {
        if (quantity === 0) {
          return prev.filter(t => t.ticketTypeId !== ticketTypeId);
        }
        return prev.map(t => 
          t.ticketTypeId === ticketTypeId ? { ...t, quantity } : t
        );
      } else if (quantity > 0) {
        return [...prev, { ticketTypeId, quantity }];
      }
      return prev;
    });
  };

  const getTicketQuantity = (ticketTypeId: string) => {
    return ticketSelections.find(t => t.ticketTypeId === ticketTypeId)?.quantity || 0;
  };

  const calculateTotal = () => {
    if (!event) return 0;
    
    return ticketSelections.reduce((total, selection) => {
      const ticketType = event.ticketTypes.find(t => t.id === selection.ticketTypeId);
      return total + (ticketType?.price || 0) * selection.quantity;
    }, 0);
  };

  const calculateFees = (subtotal: number) => {
    return subtotal * 0.029 + 0.30; // 2.9% + $0.30 processing fee
  };

  const handleSubmit = async (values: CheckoutFormData) => {
    setProcessing(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Payment successful! Redirecting to your tickets...');
      
      // Redirect to tickets page
      setTimeout(() => {
        navigate('/my-tickets');
      }, 1500);
      
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Payment failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return <LoadingScreen message="Loading checkout..." />;
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Event Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">The event you're trying to purchase tickets for doesn't exist.</p>
          <button onClick={() => navigate('/events')} className="btn-primary">
            Browse Events
          </button>
        </div>
      </div>
    );
  }

  const subtotal = calculateTotal();
  const fees = calculateFees(subtotal);
  const total = subtotal + fees;

  return (
    <>
      <Helmet>
        <title>Checkout - {event.title} - TestTicket Toy</title>
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Checkout</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Complete your ticket purchase</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Order Summary */}
            <div className="order-2 lg:order-1">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Order Summary</h2>
                
                {/* Event Info */}
                <div className="flex items-start space-x-4 mb-6 pb-6 border-b border-gray-200 dark:border-gray-700">
                  <img
                    src={event.bannerImage}
                    alt={event.title}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 dark:text-white">{event.title}</h3>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      {event.startDate.toLocaleDateString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      {event.venue.name}
                    </div>
                  </div>
                </div>

                {/* Ticket Selection */}
                <div className="space-y-4 mb-6">
                  {event.ticketTypes.map((ticketType) => (
                    <div key={ticketType.id} className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">{ticketType.name}</h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">${ticketType.price} each</p>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => updateTicketQuantity(ticketType.id, Math.max(0, getTicketQuantity(ticketType.id) - 1))}
                          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                          disabled={getTicketQuantity(ticketType.id) === 0}
                        >
                          <MinusIcon className="h-4 w-4" />
                        </button>
                        
                        <span className="w-8 text-center font-medium">
                          {getTicketQuantity(ticketType.id)}
                        </span>
                        
                        <button
                          onClick={() => updateTicketQuantity(ticketType.id, Math.min(ticketType.maxPerOrder, getTicketQuantity(ticketType.id) + 1))}
                          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                          disabled={getTicketQuantity(ticketType.id) >= ticketType.maxPerOrder}
                        >
                          <PlusIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Order Total */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
                    <span className="text-gray-900 dark:text-white">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Processing Fee</span>
                    <span className="text-gray-900 dark:text-white">${fees.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-200 dark:border-gray-700 pt-2">
                    <span className="text-gray-900 dark:text-white">Total</span>
                    <span className="text-gray-900 dark:text-white">${total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Form */}
            <div className="order-1 lg:order-2">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Payment Information</h2>
                
                <Formik
                  initialValues={{
                    firstName: user?.firstName || '',
                    lastName: user?.lastName || '',
                    email: user?.email || '',
                    phone: '',
                    cardNumber: '',
                    expiryDate: '',
                    cvv: '',
                    billingAddress: '',
                    billingCity: '',
                    billingState: '',
                    billingZip: '',
                  }}
                  validationSchema={CheckoutSchema}
                  onSubmit={handleSubmit}
                >
                  <Form className="space-y-6">
                    {/* Contact Information */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            First Name
                          </label>
                          <Field
                            name="firstName"
                            type="text"
                            className="input-field"
                          />
                          <ErrorMessage name="firstName" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Last Name
                          </label>
                          <Field
                            name="lastName"
                            type="text"
                            className="input-field"
                          />
                          <ErrorMessage name="lastName" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Email
                          </label>
                          <Field
                            name="email"
                            type="email"
                            className="input-field"
                          />
                          <ErrorMessage name="email" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Phone
                          </label>
                          <Field
                            name="phone"
                            type="tel"
                            className="input-field"
                          />
                          <ErrorMessage name="phone" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      </div>
                    </div>

                    {/* Payment Information */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                        <CreditCardIcon className="h-5 w-5 mr-2" />
                        Payment Details
                      </h3>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Card Number
                          </label>
                          <Field
                            name="cardNumber"
                            type="text"
                            placeholder="1234 5678 9012 3456"
                            className="input-field"
                          />
                          <ErrorMessage name="cardNumber" component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Expiry Date
                            </label>
                            <Field
                              name="expiryDate"
                              type="text"
                              placeholder="MM/YY"
                              className="input-field"
                            />
                            <ErrorMessage name="expiryDate" component="div" className="text-red-500 text-sm mt-1" />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              CVV
                            </label>
                            <Field
                              name="cvv"
                              type="text"
                              placeholder="123"
                              className="input-field"
                            />
                            <ErrorMessage name="cvv" component="div" className="text-red-500 text-sm mt-1" />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <button
                      type="submit"
                      disabled={processing || ticketSelections.length === 0}
                      className="w-full btn-primary flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {processing ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <LockClosedIcon className="h-5 w-5 mr-2" />
                          Complete Purchase (${total.toFixed(2)})
                        </>
                      )}
                    </button>
                  </Form>
                </Formik>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CheckoutPage;
