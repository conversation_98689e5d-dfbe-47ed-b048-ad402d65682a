import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useN<PERSON>gate, <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  CalendarIcon, 
  ChartBarIcon, 
  TicketIcon, 
  CurrencyDollarIcon, 
  UsersIcon, 
  GlobeAltIcon, 
  ArrowPathIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  ShoppingCartIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { Tab } from '@headlessui/react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { useAuthStore } from '../../store/authStore';
import { api } from '../../services/api';
import toast from 'react-hot-toast';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Time period options
const TIME_PERIODS = [
  { value: '7days', label: 'Last 7 days' },
  { value: '30days', label: 'Last 30 days' },
  { value: '90days', label: 'Last 90 days' },
  { value: '12months', label: 'Last 12 months' },
  { value: 'alltime', label: 'All time' },
];

// Chart colors
const CHART_COLORS = {
  primary: {
    main: 'rgba(14, 165, 233, 1)',
    light: 'rgba(14, 165, 233, 0.2)',
  },
  secondary: {
    main: 'rgba(217, 70, 239, 1)',
    light: 'rgba(217, 70, 239, 0.2)',
  },
  success: {
    main: 'rgba(34, 197, 94, 1)',
    light: 'rgba(34, 197, 94, 0.2)',
  },
  warning: {
    main: 'rgba(245, 158, 11, 1)',
    light: 'rgba(245, 158, 11, 0.2)',
  },
  danger: {
    main: 'rgba(239, 68, 68, 1)',
    light: 'rgba(239, 68, 68, 0.2)',
  },
  gray: {
    main: 'rgba(107, 114, 128, 1)',
    light: 'rgba(107, 114, 128, 0.2)',
  },
};

const AnalyticsPage = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const { eventId } = useParams();
  
  // State variables
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30days');
  const [events, setEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);
  const [eventData, setEventData] = useState(null);
  const [comparativeData, setComparativeData] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);

  // Fetch user's events on component mount
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await api.get('/api/v1/events', {
          params: { organizerId: user?.id, status: 'all' },
        });
        
        if (response.data.success) {
          setEvents(response.data.data.events);
          
          // If eventId is provided in URL, set it as selected
          if (eventId) {
            const event = response.data.data.events.find(e => e.id === eventId);
            if (event) {
              setSelectedEvent(event);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching events:', error);
        toast.error('Failed to load events');
      }
    };

    fetchEvents();
  }, [user?.id, eventId]);

  // Fetch dashboard analytics when period changes
  useEffect(() => {
    const fetchDashboardAnalytics = async () => {
      if (!user?.id) return;
      
      setLoading(true);
      
      try {
        const response = await api.get('/api/v1/analytics/dashboard', {
          params: { period },
        });
        
        if (response.data.success) {
          setDashboardData(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard analytics:', error);
        toast.error('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardAnalytics();
  }, [user?.id, period]);

  // Fetch event-specific analytics when selected event or period changes
  useEffect(() => {
    const fetchEventAnalytics = async () => {
      if (!selectedEvent) return;
      
      setLoading(true);
      
      try {
        const response = await api.get(`/api/v1/analytics/events/${selectedEvent.id}`, {
          params: { period },
        });
        
        if (response.data.success) {
          setEventData(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching event analytics:', error);
        toast.error('Failed to load event analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchEventAnalytics();
  }, [selectedEvent, period]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Handle period change
  const handlePeriodChange = (e) => {
    setPeriod(e.target.value);
  };

  // Handle event selection
  const handleEventChange = (e) => {
    const eventId = e.target.value;
    const event = events.find(e => e.id === eventId);
    setSelectedEvent(event);
    
    // Update URL without reloading
    if (event) {
      navigate(`/organizer/analytics/${event.id}`, { replace: true });
    } else {
      navigate('/organizer/analytics', { replace: true });
    }
  };

  // Handle export
  const handleExport = async (format = 'csv') => {
    if (!selectedEvent) return;
    
    try {
      const response = await api.get(`/api/v1/analytics/events/${selectedEvent.id}/export`, {
        params: { format },
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `analytics_${selectedEvent.title}_${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success('Export successful');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    if (selectedTab === 0) {
      // Refresh dashboard data
      const fetchDashboardAnalytics = async () => {
        setLoading(true);
        try {
          const response = await api.get('/api/v1/analytics/dashboard', {
            params: { period },
          });
          
          if (response.data.success) {
            setDashboardData(response.data.data);
            toast.success('Dashboard data refreshed');
          }
        } catch (error) {
          console.error('Error refreshing dashboard analytics:', error);
          toast.error('Failed to refresh analytics data');
        } finally {
          setLoading(false);
        }
      };

      fetchDashboardAnalytics();
    } else if (selectedTab === 1 && selectedEvent) {
      // Refresh event data
      const fetchEventAnalytics = async () => {
        setLoading(true);
        try {
          const response = await api.get(`/api/v1/analytics/events/${selectedEvent.id}`, {
            params: { period },
          });
          
          if (response.data.success) {
            setEventData(response.data.data);
            toast.success('Event data refreshed');
          }
        } catch (error) {
          console.error('Error refreshing event analytics:', error);
          toast.error('Failed to refresh event analytics data');
        } finally {
          setLoading(false);
        }
      };

      fetchEventAnalytics();
    }
  };

  // Prepare chart data for dashboard sales
  const prepareDashboardSalesChart = () => {
    if (!dashboardData || !dashboardData.dailySales) return null;
    
    return {
      labels: dashboardData.dailySales.map(day => day.date),
      datasets: [
        {
          label: 'Tickets Sold',
          data: dashboardData.dailySales.map(day => day.tickets),
          borderColor: CHART_COLORS.primary.main,
          backgroundColor: CHART_COLORS.primary.light,
          fill: true,
          tension: 0.4,
          yAxisID: 'y',
        },
        {
          label: 'Revenue ($)',
          data: dashboardData.dailySales.map(day => day.revenue),
          borderColor: CHART_COLORS.success.main,
          backgroundColor: CHART_COLORS.success.light,
          fill: true,
          tension: 0.4,
          yAxisID: 'y1',
        }
      ]
    };
  };

  // Prepare chart data for ticket type distribution
  const prepareTicketTypeChart = () => {
    if (!dashboardData || !dashboardData.tickets || !dashboardData.tickets.typeDistribution) return null;
    
    return {
      labels: dashboardData.tickets.typeDistribution.map(type => type.name),
      datasets: [
        {
          data: dashboardData.tickets.typeDistribution.map(type => type.count),
          backgroundColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare chart data for traffic sources
  const prepareTrafficSourcesChart = () => {
    if (!dashboardData || !dashboardData.trafficSources) return null;
    
    const sources = dashboardData.trafficSources;
    const labels = Object.keys(sources);
    const data = labels.map(key => sources[key].count);
    
    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare chart data for event sales
  const prepareEventSalesChart = () => {
    if (!eventData || !eventData.dailySales) return null;
    
    return {
      labels: eventData.dailySales.map(day => day.date),
      datasets: [
        {
          label: 'Tickets Sold',
          data: eventData.dailySales.map(day => day.tickets),
          borderColor: CHART_COLORS.primary.main,
          backgroundColor: CHART_COLORS.primary.light,
          fill: true,
          tension: 0.4,
        }
      ]
    };
  };

  // Prepare chart data for hourly sales distribution
  const prepareHourlySalesChart = () => {
    if (!eventData || !eventData.hourlySales) return null;
    
    return {
      labels: eventData.hourlySales.map(hour => `${hour.hour}:00`),
      datasets: [
        {
          label: 'Tickets Sold',
          data: eventData.hourlySales.map(hour => hour.count),
          backgroundColor: CHART_COLORS.primary.main,
          borderColor: CHART_COLORS.primary.main,
          borderWidth: 1,
        }
      ]
    };
  };

  // Prepare chart data for event ticket types
  const prepareEventTicketTypesChart = () => {
    if (!eventData || !eventData.tickets || !eventData.tickets.byType) return null;
    
    return {
      labels: eventData.tickets.byType.map(type => type.name),
      datasets: [
        {
          data: eventData.tickets.byType.map(type => type.sold),
          backgroundColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.success.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.danger.main,
            CHART_COLORS.gray.main,
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare chart data for conversion funnel
  const prepareConversionFunnelChart = () => {
    if (!eventData || !eventData.traffic) return null;
    
    const { views, uniqueVisitors, checkoutStarts, checkoutCompletions } = eventData.traffic;
    
    return {
      labels: ['Views', 'Unique Visitors', 'Checkout Starts', 'Checkouts Completed'],
      datasets: [
        {
          label: 'Conversion Funnel',
          data: [views, uniqueVisitors, checkoutStarts, checkoutCompletions],
          backgroundColor: [
            CHART_COLORS.primary.light,
            CHART_COLORS.secondary.light,
            CHART_COLORS.warning.light,
            CHART_COLORS.success.light,
          ],
          borderColor: [
            CHART_COLORS.primary.main,
            CHART_COLORS.secondary.main,
            CHART_COLORS.warning.main,
            CHART_COLORS.success.main,
          ],
          borderWidth: 1,
        }
      ]
    };
  };

  // Calculate percentage change
  const calculatePercentageChange = (current, previous) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  return (
    <>
      <Helmet>
        <title>Analytics - TestTicket Toy</title>
      </Helmet>

      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
            <p className="mt-1 text-gray-600 dark:text-gray-400">
              Track and analyze your events performance
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              Refresh
            </button>
            {selectedEvent && (
              <button
                onClick={() => handleExport('csv')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                Export CSV
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
          <div className="mb-4 md:mb-0">
            <label htmlFor="period" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Time Period
            </label>
            <select
              id="period"
              name="period"
              value={period}
              onChange={handlePeriodChange}
              className="form-control py-1 px-2 text-sm"
            >
              {TIME_PERIODS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div className="mb-4 md:mb-0 md:flex-1">
            <label htmlFor="event" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Event
            </label>
            <select
              id="event"
              name="event"
              value={selectedEvent?.id || ''}
              onChange={handleEventChange}
              className="form-control py-1 px-2 text-sm w-full"
            >
              <option value="">All Events</option>
              {events.map((event) => (
                <option key={event.id} value={event.id}>
                  {event.title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tab navigation */}
      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 dark:bg-gray-700 p-1 mb-6">
          <Tab
            className={({ selected }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${
                selected
                  ? 'bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 shadow'
                  : 'text-gray-700 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'
              }`
            }
          >
            Dashboard
          </Tab>
          <Tab
            className={({ selected }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${
                selected
                  ? 'bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 shadow'
                  : 'text-gray-700 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'
              }`
            }
            disabled={!selectedEvent}
          >
            Event Details
          </Tab>
          <Tab
            className={({ selected }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${
                selected
                  ? 'bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 shadow'
                  : 'text-gray-700 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'
              }`
            }
          >
            Compare Events
          </Tab>
        </Tab.List>
        <Tab.Panels>
          {/* Dashboard Tab */}
          <Tab.Panel>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
              </div>
            ) : dashboardData ? (
              <>
                {/* Stats cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {/* Revenue */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Net Revenue</h3>
                      <div className="p-2 bg-primary-100 rounded-full">
                        <CurrencyDollarIcon className="h-5 w-5 text-primary-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {formatCurrency(dashboardData.revenue.net)}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      {dashboardData.revenue.percentChange >= 0 ? (
                        <>
                          <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600">
                            {dashboardData.revenue.percentChange.toFixed(1)}%
                          </span>
                        </>
                      ) : (
                        <>
                          <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-600">
                            {Math.abs(dashboardData.revenue.percentChange).toFixed(1)}%
                          </span>
                        </>
                      )}
                      <span className="ml-1 text-gray-500">vs previous period</span>
                    </div>
                  </div>

                  {/* Tickets sold */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Tickets Sold</h3>
                      <div className="p-2 bg-secondary-100 rounded-full">
                        <TicketIcon className="h-5 w-5 text-secondary-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {dashboardData.tickets.sold.toLocaleString()}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      {dashboardData.tickets.percentChange >= 0 ? (
                        <>
                          <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600">
                            {dashboardData.tickets.percentChange.toFixed(1)}%
                          </span>
                        </>
                      ) : (
                        <>
                          <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-600">
                            {Math.abs(dashboardData.tickets.percentChange).toFixed(1)}%
                          </span>
                        </>
                      )}
                      <span className="ml-1 text-gray-500">vs previous period</span>
                    </div>
                  </div>

                  {/* Upcoming events */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Upcoming Events</h3>
                      <div className="p-2 bg-warning-100 rounded-full">
                        <CalendarIcon className="h-5 w-5 text-warning-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {dashboardData.events.upcoming}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      {dashboardData.events.percentChange >= 0 ? (
                        <>
                          <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600">
                            {dashboardData.events.percentChange.toFixed(1)}%
                          </span>
                        </>
                      ) : (
                        <>
                          <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-600">
                            {Math.abs(dashboardData.events.percentChange).toFixed(1)}%
                          </span>
                        </>
                      )}
                      <span className="ml-1 text-gray-500">vs previous period</span>
                    </div>
                  </div>

                  {/* Total attendees */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Total Attendees</h3>
                      <div className="p-2 bg-success-100 rounded-full">
                        <UsersIcon className="h-5 w-5 text-success-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {dashboardData.attendees.total.toLocaleString()}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      {dashboardData.attendees.percentChange >= 0 ? (
                        <>
                          <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600">
                            {dashboardData.attendees.percentChange.toFixed(1)}%
                          </span>
                        </>
                      ) : (
                        <>
                          <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-600">
                            {Math.abs(dashboardData.attendees.percentChange).toFixed(1)}%
                          </span>
                        </>
                      )}
                      <span className="ml-1 text-gray-500">vs previous period</span>
                    </div>
                  </div>
                </div>

                {/* Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                  {/* Sales and revenue chart */}
                  <div className="card lg:col-span-2">
                    <div className="card-header flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Sales & Revenue</h2>
                    </div>
                    <div className="card-body">
                      {prepareDashboardSalesChart() && (
                        <Line 
                          data={prepareDashboardSalesChart()}
                          options={{
                            responsive: true,
                            interaction: {
                              mode: 'index',
                              intersect: false,
                            },
                            scales: {
                              y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                  display: true,
                                  text: 'Tickets',
                                },
                                grid: {
                                  drawOnChartArea: false,
                                },
                              },
                              y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                  display: true,
                                  text: 'Revenue ($)',
                                },
                                grid: {
                                  drawOnChartArea: false,
                                },
                              },
                            },
                            plugins: {
                              legend: {
                                position: 'top',
                              },
                              tooltip: {
                                callbacks: {
                                  label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                      label += ': ';
                                    }
                                    if (context.dataset.label === 'Revenue ($)') {
                                      label += formatCurrency(context.raw);
                                    } else {
                                      label += context.raw;
                                    }
                                    return label;
                                  }
                                }
                              }
                            },
                          }}
                        />
                      )}
                    </div>
                  </div>

                  {/* Ticket types chart */}
                  <div className="card">
                    <div className="card-header flex justify-between items-center">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ticket Types</h2>
                    </div>
                    <div className="card-body flex flex-col items-center justify-center">
                      {prepareTicketTypeChart() && (
                        <div className="w-full max-w-xs">
                          <Doughnut 
                            data={prepareTicketTypeChart()}
                            options={{
                              responsive: true,
                              plugins: {
                                legend: {
                                  position: 'bottom',
                                },
                                tooltip: {
                                  callbacks: {
                                    label: function(context) {
                                      const label = context.label || '';
                                      const value = context.raw;
                                      const percentage = Math.round(context.parsed * 10) / 10;
                                      return `${label}: ${value} (${percentage}%)`;
                                    }
                                  }
                                }
                              },
                              cutout: '70%',
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Traffic sources and top events */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  {/* Traffic sources */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Traffic Sources</h2>
                    </div>
                    <div className="card-body">
                      {prepareTrafficSourcesChart() && (
                        <div className="flex flex-col md:flex-row items-center">
                          <div className="w-full md:w-1/2">
                            <Doughnut 
                              data={prepareTrafficSourcesChart()}
                              options={{
                                responsive: true,
                                plugins: {
                                  legend: {
                                    position: 'bottom',
                                  },
                                },
                                cutout: '70%',
                              }}
                            />
                          </div>
                          <div className="w-full md:w-1/2 mt-4 md:mt-0">
                            <ul className="space-y-2">
                              {Object.entries(dashboardData.trafficSources).map(([key, value]) => (
                                <li key={key} className="flex items-center justify-between">
                                  <span className="text-sm capitalize">{key}</span>
                                  <span className="text-sm font-medium">{value.percentage.toFixed(1)}%</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Top selling events */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Top Selling Events</h2>
                    </div>
                    <div className="card-body p-0">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                          <thead className="bg-gray-50 dark:bg-gray-800">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Event
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tickets Sold
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Revenue
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {dashboardData.events.topSelling && dashboardData.events.topSelling.map((event) => (
                              <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <Link to={`/organizer/events/${event.id}`} className="text-sm font-medium text-gray-900 dark:text-white hover:text-primary-600">
                                    {event.title}
                                  </Link>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                  {event.ticketsSold.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                  {formatCurrency(event.revenue)}
                                </td>
                              </tr>
                            ))}
                            {(!dashboardData.events.topSelling || dashboardData.events.topSelling.length === 0) && (
                              <tr>
                                <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                  No event data available
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">No analytics data available</p>
              </div>
            )}
          </Tab.Panel>

          {/* Event Details Tab */}
          <Tab.Panel>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
              </div>
            ) : eventData ? (
              <>
                {/* Event header */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{eventData.event.title}</h2>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      <span>
                        {new Date(eventData.event.startDate).toLocaleDateString()} - {new Date(eventData.event.endDate).toLocaleDateString()}
                      </span>
                    </div>
                    {eventData.event.venue && (
                      <div className="flex items-center">
                        <GlobeAltIcon className="h-4 w-4 mr-1" />
                        <span>
                          {eventData.event.venue}, {eventData.event.city}, {eventData.event.country}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 mr-1" />
                      <span>{eventData.event.category?.name || 'Uncategorized'}</span>
                    </div>
                  </div>
                </div>

                {/* Stats cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {/* Revenue */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Net Revenue</h3>
                      <div className="p-2 bg-primary-100 rounded-full">
                        <CurrencyDollarIcon className="h-5 w-5 text-primary-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {formatCurrency(eventData.revenue.net)}
                    </div>
                    <div className="mt-2 flex items-center justify-between text-sm">
                      <span className="text-gray-500">Total: {formatCurrency(eventData.revenue.total)}</span>
                      <span className="text-gray-500">Refunds: {formatCurrency(eventData.revenue.refunds)}</span>
                    </div>
                  </div>

                  {/* Tickets sold */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Tickets Sold</h3>
                      <div className="p-2 bg-secondary-100 rounded-full">
                        <TicketIcon className="h-5 w-5 text-secondary-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {eventData.tickets.totalSold.toLocaleString()}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="bg-secondary-600 h-1.5 rounded-full" 
                          style={{ width: `${(eventData.tickets.totalSold / (eventData.tickets.totalSold + eventData.tickets.totalAvailable)) * 100}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-gray-500">
                        {Math.round((eventData.tickets.totalSold / (eventData.tickets.totalSold + eventData.tickets.totalAvailable)) * 100)}% sold
                      </span>
                    </div>
                  </div>

                  {/* Page views */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Page Views</h3>
                      <div className="p-2 bg-warning-100 rounded-full">
                        <EyeIcon className="h-5 w-5 text-warning-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {eventData.traffic.views.toLocaleString()}
                    </div>
                    <div className="mt-2 flex items-center text-sm">
                      <span className="text-gray-500">
                        {eventData.traffic.uniqueVisitors.toLocaleString()} unique visitors
                      </span>
                    </div>
                  </div>

                  {/* Conversion rate */}
                  <div className="dashboard-stat-card">
                    <div className="flex items-center justify-between">
                      <h3 className="dashboard-stat-label">Conversion Rate</h3>
                      <div className="p-2 bg-success-100 rounded-full">
                        <CheckCircleIcon className="h-5 w-5 text-success-600" />
                      </div>
                    </div>
                    <div className="dashboard-stat-value text-gray-900 dark:text-white">
                      {eventData.traffic.conversionRate.toFixed(2)}%
                    </div>
                    <div className="mt-2 flex items-center justify-between text-sm">
                      <span className="text-gray-500">
                        <ShoppingCartIcon className="h-4 w-4 inline mr-1" />
                        {eventData.traffic.checkoutStarts} starts
                      </span>
                      <span className="text-gray-500">
                        <CheckCircleIcon className="h-4 w-4 inline mr-1" />
                        {eventData.traffic.checkoutCompletions} completions
                      </span>
                    </div>
                  </div>
                </div>

                {/* Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  {/* Daily sales chart */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Daily Sales</h2>
                    </div>
                    <div className="card-body">
                      {prepareEventSalesChart() && (
                        <Line 
                          data={prepareEventSalesChart()}
                          options={{
                            responsive: true,
                            plugins: {
                              legend: {
                                position: 'top',
                              },
                            },
                            scales: {
                              y: {
                                beginAtZero: true,
                              }
                            }
                          }}
                        />
                      )}
                    </div>
                  </div>

                  {/* Hourly distribution chart */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Sales by Hour</h2>
                    </div>
                    <div className="card-body">
                      {prepareHourlySalesChart() && (
                        <Bar 
                          data={prepareHourlySalesChart()}
                          options={{
                            responsive: true,
                            plugins: {
                              legend: {
                                display: false,
                              },
                              tooltip: {
                                callbacks: {
                                  title: function(context) {
                                    return `${context[0].label} - ${parseInt(context[0].label) + 1}:00`;
                                  }
                                }
                              }
                            },
                            scales: {
                              y: {
                                beginAtZero: true,
                              }
                            }
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Ticket types and conversion funnel */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  {/* Ticket types chart */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ticket Types</h2>
                    </div>
                    <div className="card-body">
                      <div className="flex flex-col md:flex-row items-center">
                        <div className="w-full md:w-1/2">
                          {prepareEventTicketTypesChart() && (
                            <Doughnut 
                              data={prepareEventTicketTypesChart()}
                              options={{
                                responsive: true,
                                plugins: {
                                  legend: {
                                    position: 'bottom',
                                  },
                                },
                                cutout: '70%',
                              }}
                            />
                          )}
                        </div>
                        <div className="w-full md:w-1/2 mt-4 md:mt-0">
                          <ul className="space-y-2">
                            {eventData.tickets.byType.map((type) => (
                              <li key={type.id} className="flex items-center justify-between">
                                <span className="text-sm">{type.name}</span>
                                <span className="text-sm font-medium">{type.sold} sold</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Conversion funnel */}
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">Conversion Funnel</h2>
                    </div>
                    <div className="card-body">
                      {prepareConversionFunnelChart() && (
                        <Bar 
                          data={prepareConversionFunnelChart()}
                          options={{
                            responsive: true,
                            indexAxis: 'y',
                            plugins: {
                              legend: {
                                display: false,
                              },
                            },
                            scales: {
                              x: {
                                beginAtZero: true,
                              }
                            }
                          }}
                        />
                      )}
                      <div className="mt-4">
                        <ul className="space-y-2">
                          <li className="flex items-center justify-between text-sm">
                            <span>Views to Unique Visitors:</span>
                            <span className="font-medium">
                              {eventData.traffic.uniqueVisitors > 0 
                                ? Math.round((eventData.traffic.uniqueVisitors / eventData.traffic.views) * 100)
                                : 0}%
                            </span>
                          </li>
                          <li className="flex items-center justify-between text-sm">
                            <span>Visitors to Checkout:</span>
                            <span className="font-medium">
                              {eventData.traffic.uniqueVisitors > 0 
                                ? Math.round((eventData.traffic.checkoutStarts / eventData.traffic.uniqueVisitors) * 100)
                                : 0}%
                            </span>
                          </li>
                          <li className="flex items-center justify-between text-sm">
                            <span>Checkout to Purchase:</span>
                            <span className="font-medium">
                              {eventData.traffic.checkoutStarts > 0 
                                ? Math.round((eventData.traffic.checkoutCompletions / eventData.traffic.checkoutStarts) * 100)
                                : 0}%
                            </span>
                          </li>
                          <li className="flex items-center justify-between text-sm font-medium">
                            <span>Overall Conversion:</span>
                            <span>
                              {eventData.traffic.uniqueVisitors > 0 
                                ? Math.round((eventData.traffic.checkoutCompletions / eventData.traffic.uniqueVisitors) * 100)
                                : 0}%
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent transactions */}
                <div className="card mb-8">
                  <div className="card-header">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Transactions</h2>
                  </div>
                  <div className="card-body p-0">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Customer
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Tickets
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {eventData.transactions && eventData.transactions.slice(0, 10).map((transaction) => (
                            <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {new Date(transaction.date).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {transaction.customer}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {transaction.email}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {formatCurrency(transaction.amount)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  transaction.status === 'COMPLETED'
                                    ? 'bg-green-100 text-green-800'
                                    : transaction.status === 'REFUNDED'
                                    ? 'bg-red-100 text-red-800'
                                    : transaction.status === 'PARTIALLY_REFUNDED'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {transaction.status.replace('_', ' ')}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {transaction.ticketCount}
                              </td>
                            </tr>
                          ))}
                          {(!eventData.transactions || eventData.transactions.length === 0) && (
                            <tr>
                              <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                No transactions available
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">Select an event to view detailed analytics</p>
              </div>
            )}
          </Tab.Panel>

          {/* Compare Events Tab */}
          <Tab.Panel>
            <div className="text-center py-12">
              <h2 className="text-xl font-medium text-gray-900 dark:text-white mb-4">Event Comparison</h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Coming soon! This feature will allow you to compare analytics between multiple events.
              </p>
              <Link to="/organizer/events" className="btn-primary">
                View All Events
              </Link>
            </div>
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </>
  );
};

export default AnalyticsPage;
