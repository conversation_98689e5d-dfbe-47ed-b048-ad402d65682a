import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  CalendarIcon, 
  TicketIcon, 
  CurrencyDollarIcon, 
  UsersIcon, 
  ArrowUpIcon, 
  ArrowDownIcon,
  EllipsisHorizontalIcon,
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../store/authStore';
import { OrganizerUser, OrganizerTier } from '../../types/user';
import { EventStatus } from '../../types/event';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Mock data for the dashboard
const MOCK_STATS = {
  totalRevenue: 24850.75,
  previousRevenue: 18920.50,
  ticketsSold: 1254,
  previousTicketsSold: 980,
  upcomingEvents: 8,
  previousUpcomingEvents: 5,
  totalAttendees: 3200,
  previousAttendees: 2800,
};

const MOCK_RECENT_EVENTS = [
  {
    id: '1',
    title: 'Summer Music Festival 2025',
    date: 'Jun 15-18, 2025',
    location: 'Central Park, New York',
    status: EventStatus.PUBLISHED,
    ticketsSold: 450,
    totalTickets: 1000,
    revenue: 12500.00,
  },
  {
    id: '2',
    title: 'Tech Conference 2025',
    date: 'Jul 10-12, 2025',
    location: 'Convention Center, San Francisco',
    status: EventStatus.PUBLISHED,
    ticketsSold: 320,
    totalTickets: 500,
    revenue: 9600.00,
  },
  {
    id: '3',
    title: 'Food & Wine Festival',
    date: 'Aug 5-7, 2025',
    location: 'Waterfront Park, Chicago',
    status: EventStatus.DRAFT,
    ticketsSold: 0,
    totalTickets: 750,
    revenue: 0,
  },
];

const MOCK_RECENT_TRANSACTIONS = [
  {
    id: '1',
    customerName: 'John Smith',
    event: 'Summer Music Festival 2025',
    date: '2025-06-01T14:32:45Z',
    amount: 149.99,
    status: 'completed',
  },
  {
    id: '2',
    customerName: 'Emma Johnson',
    event: 'Tech Conference 2025',
    date: '2025-06-01T12:15:22Z',
    amount: 199.99,
    status: 'completed',
  },
  {
    id: '3',
    customerName: 'Michael Brown',
    event: 'Summer Music Festival 2025',
    date: '2025-05-31T18:45:11Z',
    amount: 299.99,
    status: 'completed',
  },
  {
    id: '4',
    customerName: 'Sarah Davis',
    event: 'Tech Conference 2025',
    date: '2025-05-31T10:22:33Z',
    amount: 199.99,
    status: 'refunded',
  },
  {
    id: '5',
    customerName: 'David Wilson',
    event: 'Summer Music Festival 2025',
    date: '2025-05-30T16:18:05Z',
    amount: 149.99,
    status: 'completed',
  },
];

// Helper function to get tier badge color
const getTierColor = (tier: OrganizerTier) => {
  switch (tier) {
    case OrganizerTier.STARTER:
      return 'bg-gray-100 text-gray-800';
    case OrganizerTier.GROWTH:
      return 'bg-blue-100 text-blue-800';
    case OrganizerTier.PROFESSIONAL:
      return 'bg-purple-100 text-purple-800';
    case OrganizerTier.ENTERPRISE:
      return 'bg-indigo-100 text-indigo-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
};

// Helper function to calculate percentage change
const calculatePercentageChange = (current: number, previous: number) => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

const DashboardPage = () => {
  const { user } = useAuthStore();
  const organizerUser = user as OrganizerUser;
  const organizerProfile = organizerUser?.organizerProfile;

  // State for chart data
  const [salesChartData, setSalesChartData] = useState<any>(null);
  const [ticketTypeChartData, setTicketTypeChartData] = useState<any>(null);

  // Generate chart data on component mount
  useEffect(() => {
    // Sales chart data (last 30 days)
    const labels = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - 29 + i);
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    // Generate some random sales data
    const salesData = Array.from({ length: 30 }, () => 
      Math.floor(Math.random() * 50) + 10
    );
    
    // Generate some random revenue data (correlated with sales)
    const revenueData = salesData.map(sales => 
      sales * (Math.random() * 20 + 30)
    );

    setSalesChartData({
      labels,
      datasets: [
        {
          label: 'Tickets Sold',
          data: salesData,
          borderColor: '#0ea5e9',
          backgroundColor: 'rgba(14, 165, 233, 0.1)',
          fill: true,
          tension: 0.4,
          yAxisID: 'y',
        },
        {
          label: 'Revenue ($)',
          data: revenueData,
          borderColor: '#22c55e',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: true,
          tension: 0.4,
          yAxisID: 'y1',
        }
      ]
    });

    // Ticket types chart data
    setTicketTypeChartData({
      labels: ['General Admission', 'VIP', 'Early Bird', 'Group'],
      datasets: [
        {
          data: [65, 15, 12, 8],
          backgroundColor: [
            'rgba(14, 165, 233, 0.7)',
            'rgba(217, 70, 239, 0.7)',
            'rgba(245, 158, 11, 0.7)',
            'rgba(34, 197, 94, 0.7)',
          ],
          borderColor: [
            'rgba(14, 165, 233, 1)',
            'rgba(217, 70, 239, 1)',
            'rgba(245, 158, 11, 1)',
            'rgba(34, 197, 94, 1)',
          ],
          borderWidth: 1,
        },
      ],
    });
  }, []);

  return (
    <>
      <Helmet>
        <title>Dashboard - TestTicket Toy</title>
      </Helmet>

      {/* Welcome section */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Welcome back, {organizerUser?.firstName}!
            </h1>
            <p className="mt-1 text-gray-600 dark:text-gray-400">
              Here's what's happening with your events today.
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTierColor(organizerProfile?.tier || OrganizerTier.STARTER)}`}>
              {organizerProfile?.tier || OrganizerTier.STARTER} TIER
            </span>
            <Link to="/organizer/events/create" className="ml-4 btn-primary flex items-center">
              <PlusIcon className="h-5 w-5 mr-1" />
              Create Event
            </Link>
          </div>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Revenue */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <h3 className="dashboard-stat-label">Total Revenue</h3>
            <div className="p-2 bg-primary-100 rounded-full">
              <CurrencyDollarIcon className="h-5 w-5 text-primary-600" />
            </div>
          </div>
          <div className="dashboard-stat-value text-gray-900 dark:text-white">
            {formatCurrency(MOCK_STATS.totalRevenue)}
          </div>
          <div className="mt-2 flex items-center text-sm">
            {calculatePercentageChange(MOCK_STATS.totalRevenue, MOCK_STATS.previousRevenue) >= 0 ? (
              <>
                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">
                  {calculatePercentageChange(MOCK_STATS.totalRevenue, MOCK_STATS.previousRevenue).toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">
                  {Math.abs(calculatePercentageChange(MOCK_STATS.totalRevenue, MOCK_STATS.previousRevenue)).toFixed(1)}%
                </span>
              </>
            )}
            <span className="ml-1 text-gray-500">vs last month</span>
          </div>
        </div>

        {/* Tickets sold */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <h3 className="dashboard-stat-label">Tickets Sold</h3>
            <div className="p-2 bg-secondary-100 rounded-full">
              <TicketIcon className="h-5 w-5 text-secondary-600" />
            </div>
          </div>
          <div className="dashboard-stat-value text-gray-900 dark:text-white">
            {MOCK_STATS.ticketsSold.toLocaleString()}
          </div>
          <div className="mt-2 flex items-center text-sm">
            {calculatePercentageChange(MOCK_STATS.ticketsSold, MOCK_STATS.previousTicketsSold) >= 0 ? (
              <>
                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">
                  {calculatePercentageChange(MOCK_STATS.ticketsSold, MOCK_STATS.previousTicketsSold).toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">
                  {Math.abs(calculatePercentageChange(MOCK_STATS.ticketsSold, MOCK_STATS.previousTicketsSold)).toFixed(1)}%
                </span>
              </>
            )}
            <span className="ml-1 text-gray-500">vs last month</span>
          </div>
        </div>

        {/* Upcoming events */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <h3 className="dashboard-stat-label">Upcoming Events</h3>
            <div className="p-2 bg-warning-100 rounded-full">
              <CalendarIcon className="h-5 w-5 text-warning-600" />
            </div>
          </div>
          <div className="dashboard-stat-value text-gray-900 dark:text-white">
            {MOCK_STATS.upcomingEvents}
          </div>
          <div className="mt-2 flex items-center text-sm">
            {calculatePercentageChange(MOCK_STATS.upcomingEvents, MOCK_STATS.previousUpcomingEvents) >= 0 ? (
              <>
                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">
                  {calculatePercentageChange(MOCK_STATS.upcomingEvents, MOCK_STATS.previousUpcomingEvents).toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">
                  {Math.abs(calculatePercentageChange(MOCK_STATS.upcomingEvents, MOCK_STATS.previousUpcomingEvents)).toFixed(1)}%
                </span>
              </>
            )}
            <span className="ml-1 text-gray-500">vs last month</span>
          </div>
        </div>

        {/* Total attendees */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <h3 className="dashboard-stat-label">Total Attendees</h3>
            <div className="p-2 bg-success-100 rounded-full">
              <UsersIcon className="h-5 w-5 text-success-600" />
            </div>
          </div>
          <div className="dashboard-stat-value text-gray-900 dark:text-white">
            {MOCK_STATS.totalAttendees.toLocaleString()}
          </div>
          <div className="mt-2 flex items-center text-sm">
            {calculatePercentageChange(MOCK_STATS.totalAttendees, MOCK_STATS.previousAttendees) >= 0 ? (
              <>
                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">
                  {calculatePercentageChange(MOCK_STATS.totalAttendees, MOCK_STATS.previousAttendees).toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">
                  {Math.abs(calculatePercentageChange(MOCK_STATS.totalAttendees, MOCK_STATS.previousAttendees)).toFixed(1)}%
                </span>
              </>
            )}
            <span className="ml-1 text-gray-500">vs last month</span>
          </div>
        </div>
      </div>

      {/* Charts section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Sales and revenue chart */}
        <div className="card lg:col-span-2">
          <div className="card-header flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Sales & Revenue</h2>
            <div className="flex items-center space-x-2">
              <select className="form-control py-1 px-2 text-sm">
                <option>Last 30 days</option>
                <option>Last 90 days</option>
                <option>Last year</option>
                <option>All time</option>
              </select>
              <button className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                <EllipsisHorizontalIcon className="h-5 w-5 text-gray-500" />
              </button>
            </div>
          </div>
          <div className="card-body">
            {salesChartData && (
              <Line 
                data={salesChartData}
                options={{
                  responsive: true,
                  interaction: {
                    mode: 'index',
                    intersect: false,
                  },
                  scales: {
                    y: {
                      type: 'linear',
                      display: true,
                      position: 'left',
                      title: {
                        display: true,
                        text: 'Tickets',
                      },
                      grid: {
                        drawOnChartArea: false,
                      },
                    },
                    y1: {
                      type: 'linear',
                      display: true,
                      position: 'right',
                      title: {
                        display: true,
                        text: 'Revenue ($)',
                      },
                      grid: {
                        drawOnChartArea: false,
                      },
                    },
                  },
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                  },
                }}
              />
            )}
          </div>
        </div>

        {/* Ticket types chart */}
        <div className="card">
          <div className="card-header flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ticket Types</h2>
            <button className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
              <EllipsisHorizontalIcon className="h-5 w-5 text-gray-500" />
            </button>
          </div>
          <div className="card-body flex flex-col items-center justify-center">
            {ticketTypeChartData && (
              <div className="w-full max-w-xs">
                <Doughnut 
                  data={ticketTypeChartData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'bottom',
                      },
                    },
                    cutout: '70%',
                  }}
                />
              </div>
            )}
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                Based on total ticket sales across all events
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent events and transactions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent events */}
        <div className="card">
          <div className="card-header flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Events</h2>
            <Link to="/organizer/events" className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all
            </Link>
          </div>
          <div className="card-body p-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Event
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Sales
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Revenue
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {MOCK_RECENT_EVENTS.map((event) => (
                    <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              <Link to={`/organizer/events/${event.id}`} className="hover:text-primary-600">
                                {event.title}
                              </Link>
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              <div className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-1" />
                                {event.date}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          event.status === EventStatus.PUBLISHED
                            ? 'bg-green-100 text-green-800'
                            : event.status === EventStatus.DRAFT
                            ? 'bg-gray-100 text-gray-800'
                            : event.status === EventStatus.CANCELLED
                            ? 'bg-red-100 text-red-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {event.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {event.ticketsSold} / {event.totalTickets}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div 
                            className="bg-primary-600 h-1.5 rounded-full" 
                            style={{ width: `${(event.ticketsSold / event.totalTickets) * 100}%` }}
                          ></div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {formatCurrency(event.revenue)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent transactions */}
        <div className="card">
          <div className="card-header flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Transactions</h2>
            <Link to="/organizer/tickets" className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              View all
            </Link>
          </div>
          <div className="card-body p-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Customer
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Event
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {MOCK_RECENT_TRANSACTIONS.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {transaction.customerName}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          <div className="flex items-center">
                            <ClockIcon className="h-3 w-3 mr-1" />
                            {formatDate(transaction.date)}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {transaction.event}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {formatCurrency(transaction.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {transaction.status === 'completed' ? (
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                          ) : (
                            <ExclamationCircleIcon className="h-4 w-4 text-red-500 mr-1" />
                          )}
                          <span className={`text-xs ${
                            transaction.status === 'completed' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Quick actions */}
      <div className="mt-8">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Link to="/organizer/events/create" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-full inline-flex items-center justify-center mb-2">
              <PlusIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Create Event</p>
          </Link>
          <Link to="/organizer/promos" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-secondary-100 dark:bg-secondary-900 rounded-full inline-flex items-center justify-center mb-2">
              <CurrencyDollarIcon className="h-6 w-6 text-secondary-600 dark:text-secondary-400" />
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Create Promo</p>
          </Link>
          <Link to="/organizer/analytics" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-warning-100 dark:bg-warning-900 rounded-full inline-flex items-center justify-center mb-2">
              <ChartBarIcon className="h-6 w-6 text-warning-600 dark:text-warning-400" />
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">View Analytics</p>
          </Link>
          <Link to="/organizer/marketing" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-success-100 dark:bg-success-900 rounded-full inline-flex items-center justify-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6 text-success-600 dark:text-success-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 110-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 01-1.44-4.282m3.102.069a18.03 18.03 0 01-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 018.835 2.535M10.34 6.66a23.847 23.847 0 008.835-2.535m0 0A23.74 23.74 0 0018.795 3m.38 1.125a23.91 23.91 0 011.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 001.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 010 3.46" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Marketing</p>
          </Link>
          <Link to="/organizer/team" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full inline-flex items-center justify-center mb-2">
              <UsersIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Manage Team</p>
          </Link>
          <Link to="/organizer/settings" className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-soft text-center hover:shadow-soft-md transition-shadow">
            <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-full inline-flex items-center justify-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6 text-gray-600 dark:text-gray-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Settings</p>
          </Link>
        </div>
      </div>
    </>
  );
};

export default DashboardPage;
