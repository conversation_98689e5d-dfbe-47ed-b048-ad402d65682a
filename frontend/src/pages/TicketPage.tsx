import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { 
  CalendarIcon, 
  MapPinIcon, 
  ClockIcon,
  QrCodeIcon,
  ShareIcon,
  PrinterIcon,
  DownloadIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import LoadingScreen from '../components/common/LoadingScreen';
import toast from 'react-hot-toast';

interface Ticket {
  id: string;
  orderNumber: string;
  ticketNumber: string;
  qrCode: string;
  status: 'valid' | 'used' | 'cancelled';
  event: {
    id: string;
    title: string;
    bannerImage: string;
    startDate: Date;
    endDate: Date;
    venue: {
      name: string;
      address: string;
    };
  };
  ticketType: {
    name: string;
    price: number;
    currency: string;
  };
  attendee: {
    firstName: string;
    lastName: string;
    email: string;
  };
  purchaseDate: Date;
  transferredFrom?: string;
}

const TicketPage = () => {
  const { ticketId } = useParams<{ ticketId: string }>();
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTicket = async () => {
      try {
        // Mock ticket data
        const mockTicket: Ticket = {
          id: ticketId || '1',
          orderNumber: 'TT-2024-001234',
          ticketNumber: 'TKT-789012345',
          qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(`ticket:${ticketId}`),
          status: 'valid',
          event: {
            id: '1',
            title: 'Summer Music Festival 2024',
            bannerImage: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=400',
            startDate: new Date('2024-07-15T18:00:00'),
            endDate: new Date('2024-07-15T23:00:00'),
            venue: {
              name: 'Central Park',
              address: '123 Park Ave, New York, NY 10001'
            }
          },
          ticketType: {
            name: 'General Admission',
            price: 75.00,
            currency: 'USD'
          },
          attendee: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          },
          purchaseDate: new Date('2024-01-15T10:30:00')
        };

        setTicket(mockTicket);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching ticket:', error);
        toast.error('Failed to load ticket details');
        setLoading(false);
      }
    };

    if (ticketId) {
      fetchTicket();
    }
  }, [ticketId]);

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // In a real app, this would generate and download a PDF
    toast.success('Ticket download started');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `My ticket for ${ticket?.event.title}`,
        text: `Check out my ticket for ${ticket?.event.title}!`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Ticket link copied to clipboard!');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'used':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'valid':
        return 'Valid';
      case 'used':
        return 'Used';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return <LoadingScreen message="Loading ticket..." />;
  }

  if (!ticket) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Ticket Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">The ticket you're looking for doesn't exist or you don't have access to it.</p>
          <button onClick={() => window.history.back()} className="btn-primary">
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Ticket - {ticket.event.title} - TestTicket Toy</title>
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Your Ticket</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Present this ticket at the event entrance</p>
          </div>

          {/* Ticket Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden print:shadow-none">
            {/* Event Banner */}
            <div className="relative h-48">
              <img
                src={ticket.event.bannerImage}
                alt={ticket.event.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              
              {/* Status Badge */}
              <div className="absolute top-4 right-4">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(ticket.status)}`}>
                  {ticket.status === 'valid' && <CheckCircleIcon className="h-4 w-4 mr-1" />}
                  {getStatusText(ticket.status)}
                </span>
              </div>

              {/* Event Title */}
              <div className="absolute bottom-4 left-4 right-4">
                <h2 className="text-2xl font-bold text-white">{ticket.event.title}</h2>
              </div>
            </div>

            {/* Ticket Details */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-4">
                  {/* Event Details */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Event Details</h3>
                    
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {ticket.event.startDate.toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {ticket.event.startDate.toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit'
                            })} - {ticket.event.endDate.toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <MapPinIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{ticket.event.venue.name}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{ticket.event.venue.address}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Ticket Info */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Ticket Information</h3>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Ticket Type:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{ticket.ticketType.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Price:</span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          ${ticket.ticketType.price.toFixed(2)} {ticket.ticketType.currency}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Order Number:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{ticket.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Ticket Number:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{ticket.ticketNumber}</span>
                      </div>
                    </div>
                  </div>

                  {/* Attendee Info */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Attendee</h3>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {ticket.attendee.firstName} {ticket.attendee.lastName}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{ticket.attendee.email}</p>
                  </div>
                </div>

                {/* Right Column - QR Code */}
                <div className="flex flex-col items-center justify-center">
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <img
                      src={ticket.qrCode}
                      alt="Ticket QR Code"
                      className="w-48 h-48"
                    />
                  </div>
                  <p className="mt-3 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Scan this QR code at the event entrance
                  </p>
                </div>
              </div>

              {/* Purchase Info */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>Purchased on {ticket.purchaseDate.toLocaleDateString()}</span>
                  {ticket.transferredFrom && (
                    <span>Transferred from {ticket.transferredFrom}</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-wrap justify-center gap-4 print:hidden">
            <button
              onClick={handlePrint}
              className="btn-outline flex items-center"
            >
              <PrinterIcon className="h-5 w-5 mr-2" />
              Print Ticket
            </button>
            
            <button
              onClick={handleDownload}
              className="btn-outline flex items-center"
            >
              <DownloadIcon className="h-5 w-5 mr-2" />
              Download PDF
            </button>
            
            <button
              onClick={handleShare}
              className="btn-outline flex items-center"
            >
              <ShareIcon className="h-5 w-5 mr-2" />
              Share
            </button>
          </div>

          {/* Important Notes */}
          <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 print:hidden">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">Important Notes:</h3>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• Please arrive at least 30 minutes before the event starts</li>
              <li>• Bring a valid photo ID that matches the name on this ticket</li>
              <li>• This ticket is non-transferable and non-refundable</li>
              <li>• Screenshots of this ticket will not be accepted</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default TicketPage;
