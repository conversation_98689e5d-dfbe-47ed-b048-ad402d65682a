/**
 * Event-related type definitions for TestTicket Toy frontend
 */

import { User } from './user';

/**
 * Event status enum - must match backend enum values
 */
export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
}

/**
 * Event visibility enum - must match backend enum values
 */
export enum Visibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  UNLISTED = 'UNLISTED',
}

/**
 * Ticket status enum - must match backend enum values
 */
export enum TicketStatus {
  AVAILABLE = 'AVAILABLE',
  RESERVED = 'RESERVED',
  SOLD = 'SOLD',
  CHECKED_IN = 'CHECKED_IN',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

/**
 * Transaction status enum - must match backend enum values
 */
export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
}

/**
 * Payment method enum - must match backend enum values
 */
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  PAYPAL = 'PAYPAL',
  APPLE_PAY = 'APPLE_PAY',
  GOOGLE_PAY = 'GOOGLE_PAY',
  BANK_TRANSFER = 'BANK_TRANSFER',
  OTHER = 'OTHER',
}

/**
 * Discount type enum - must match backend enum values
 */
export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED_AMOUNT = 'FIXED_AMOUNT',
}

/**
 * Campaign type enum - must match backend enum values
 */
export enum CampaignType {
  EMAIL = 'EMAIL',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  SEARCH_ADS = 'SEARCH_ADS',
  DISPLAY_ADS = 'DISPLAY_ADS',
  AFFILIATE = 'AFFILIATE',
  INFLUENCER = 'INFLUENCER',
}

/**
 * Campaign status enum - must match backend enum values
 */
export enum CampaignStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

/**
 * Category interface
 */
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Base Event interface with common properties
 */
export interface Event {
  id: string;
  title: string;
  slug: string;
  description: string;
  shortDescription?: string;
  bannerImage?: string;
  galleryImages?: string[];
  startDate: string;
  endDate: string;
  timezone: string;
  isOnline: boolean;
  venue?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  currency: string;
  organizerId: string;
  organizer?: User;
  categoryId: string;
  category?: Category;
  status: EventStatus;
  visibility: Visibility;
  maxTicketsPerUser?: number;
  termsAndConditions?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Event with related data for detail views
 */
export interface EventWithDetails extends Event {
  ticketTypes: TicketType[];
  analytics?: EventAnalytics;
  savedCount?: number;
  isSaved?: boolean;
  reviewCount?: number;
  averageRating?: number;
}

/**
 * Event summary for list views
 */
export interface EventSummary {
  id: string;
  title: string;
  slug: string;
  shortDescription?: string;
  bannerImage?: string;
  startDate: string;
  endDate: string;
  venue?: string;
  city?: string;
  country?: string;
  isOnline: boolean;
  status: EventStatus;
  category?: Category;
  lowestPrice?: number;
  currency: string;
  ticketsSold?: number;
  totalCapacity?: number;
}

/**
 * Ticket type interface
 */
export interface TicketType {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  quantity: number;
  quantitySold: number;
  eventId: string;
  salesStartDate: string;
  salesEndDate: string;
  isEarlyBird: boolean;
  isVIP: boolean;
  maxPerUser?: number;
  includesAdditionalItems?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Ticket interface
 */
export interface Ticket {
  id: string;
  ticketNumber: string;
  ticketTypeId: string;
  ticketType?: TicketType;
  eventId: string;
  event?: EventSummary;
  userId?: string;
  user?: User;
  transactionId?: string;
  status: TicketStatus;
  qrCode?: string;
  checkedInAt?: string;
  transferredFrom?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Transaction interface
 */
export interface Transaction {
  id: string;
  userId: string;
  user?: User;
  amount: number;
  currency: string;
  status: TransactionStatus;
  paymentMethod: PaymentMethod;
  paymentIntentId?: string;
  paymentOrderId?: string;
  receiptUrl?: string;
  refundAmount?: number;
  refundReason?: string;
  promoCodeId?: string;
  promoCode?: PromoCode;
  tickets: Ticket[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Promo code interface
 */
export interface PromoCode {
  id: string;
  code: string;
  description?: string;
  discountType: DiscountType;
  discountValue: number;
  maxUses?: number;
  usedCount: number;
  minPurchase?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  createdBy: string;
  eventId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Event analytics interface
 */
export interface EventAnalytics {
  id: string;
  eventId: string;
  views: number;
  uniqueVisitors: number;
  checkoutStarts: number;
  checkoutCompletions: number;
  revenue: number;
  refunds: number;
  trafficSources?: Record<string, any>;
  conversionRate?: number;
  dailyStats?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Marketing campaign interface
 */
export interface MarketingCampaign {
  id: string;
  name: string;
  description?: string;
  eventId: string;
  event?: EventSummary;
  campaignType: CampaignType;
  budget?: number;
  startDate: string;
  endDate: string;
  status: CampaignStatus;
  targetAudience?: Record<string, any>;
  creativeAssets?: string[];
  results?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Saved event interface
 */
export interface SavedEvent {
  id: string;
  userId: string;
  eventId: string;
  event?: EventSummary;
  createdAt: string;
}

/**
 * Review interface
 */
export interface Review {
  id: string;
  userId: string;
  user?: User;
  eventId: string;
  event?: EventSummary;
  rating: number;
  comment?: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Event creation data interface
 */
export interface EventCreationData {
  title: string;
  description: string;
  shortDescription?: string;
  bannerImage?: File | string;
  galleryImages?: (File | string)[];
  startDate: string;
  endDate: string;
  timezone: string;
  isOnline: boolean;
  venue?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  currency: string;
  categoryId: string;
  visibility: Visibility;
  maxTicketsPerUser?: number;
  termsAndConditions?: string;
  ticketTypes: Omit<TicketType, 'id' | 'eventId' | 'createdAt' | 'updatedAt' | 'quantitySold'>[];
}

/**
 * Event update data interface
 */
export interface EventUpdateData extends Partial<EventCreationData> {
  id: string;
}

/**
 * Event search filters interface
 */
export interface EventSearchFilters {
  query?: string;
  categories?: string[];
  startDate?: string;
  endDate?: string;
  location?: string;
  radius?: number; // in km
  priceMin?: number;
  priceMax?: number;
  isOnline?: boolean;
  isInPerson?: boolean;
  sortBy?: 'date' | 'price' | 'popularity' | 'relevance';
  page?: number;
  limit?: number;
}

/**
 * Cart item interface
 */
export interface CartItem {
  ticketTypeId: string;
  eventId: string;
  quantity: number;
  ticketType: TicketType;
  event: EventSummary;
}

/**
 * Shopping cart interface
 */
export interface Cart {
  items: CartItem[];
  promoCode?: PromoCode;
  subtotal: number;
  discount: number;
  total: number;
  currency: string;
}
