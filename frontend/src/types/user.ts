/**
 * User-related type definitions for TestTicket Toy frontend
 */

/**
 * User roles enum - must match backend enum values
 */
export enum UserRole {
  CUSTOMER = 'CUSTOMER',
  ORGANIZER = 'ORGANIZER',
  ADMIN = 'ADMIN',
}

/**
 * Organizer tier levels - must match backend enum values
 */
export enum OrganizerTier {
  STARTER = 'STARTER',
  GROWTH = 'GROWTH',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE',
}

/**
 * Verification status for organizer accounts
 */
export enum VerificationStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
}

/**
 * Team member role types
 */
export enum TeamRole {
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  ANALYST = 'ANALYST',
  SUPPORT = 'SUPPORT',
}

/**
 * Base user interface with common properties
 */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  profileImage?: string;
  role: UserRole;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Social media links for organizer profiles
 */
export interface SocialLinks {
  facebook?: string;
  twitter?: string;
  instagram?: string;
  linkedin?: string;
  youtube?: string;
  website?: string;
}

/**
 * Bank account information for payouts
 */
export interface BankAccountInfo {
  accountName: string;
  accountNumber: string;
  routingNumber?: string;
  bankName: string;
  swiftCode?: string;
  iban?: string;
  country: string;
}

/**
 * Organizer profile extension
 */
export interface OrganizerProfile {
  id: string;
  userId: string;
  companyName: string;
  companyLogo?: string;
  description?: string;
  website?: string;
  socialLinks?: SocialLinks;
  tier: OrganizerTier;
  ticketsSold: number;
  totalRevenue: number;
  stripeAccountId?: string;
  paypalAccountId?: string;
  bankAccountInfo?: BankAccountInfo;
  verificationStatus: VerificationStatus;
  verificationDocuments?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Customer user with customer-specific properties
 */
export interface CustomerUser extends User {
  role: UserRole.CUSTOMER;
}

/**
 * Organizer user with organizer-specific properties
 */
export interface OrganizerUser extends User {
  role: UserRole.ORGANIZER;
  organizerProfile: OrganizerProfile;
}

/**
 * Admin user with admin-specific properties
 */
export interface AdminUser extends User {
  role: UserRole.ADMIN;
  permissions?: string[];
}

/**
 * Type guard to check if user is an organizer
 */
export function isOrganizerUser(user: User): user is OrganizerUser {
  return user.role === UserRole.ORGANIZER;
}

/**
 * Type guard to check if user is an admin
 */
export function isAdminUser(user: User): user is AdminUser {
  return user.role === UserRole.ADMIN;
}

/**
 * Team member interface
 */
export interface TeamMember {
  id: string;
  email: string;
  role: TeamRole;
  permissions: string[];
  invitedBy: string;
  inviteToken?: string;
  inviteAccepted: boolean;
  createdAt: string;
  updatedAt: string;
  user?: User;
}

/**
 * Auth state interface for the auth store
 */
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | OrganizerUser | AdminUser | null;
  error: string | null;
}

/**
 * Login credentials interface
 */
export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Registration data interface for customers
 */
export interface RegisterCustomerData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

/**
 * Registration data interface for organizers
 */
export interface RegisterOrganizerData extends RegisterCustomerData {
  companyName: string;
  website?: string;
  description?: string;
}

/**
 * Auth tokens interface
 */
export interface AuthTokens {
  token: string;
  refreshToken?: string;
}

/**
 * Password reset request interface
 */
export interface PasswordResetRequest {
  email: string;
}

/**
 * Password reset confirmation interface
 */
export interface PasswordResetConfirm {
  password: string;
  confirmPassword: string;
  token: string;
}

/**
 * Password change interface
 */
export interface PasswordChange {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * User profile update interface
 */
export interface UserProfileUpdate {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  profileImage?: File | string;
}

/**
 * Organizer profile update interface
 */
export interface OrganizerProfileUpdate {
  companyName?: string;
  companyLogo?: File | string;
  description?: string;
  website?: string;
  socialLinks?: SocialLinks;
  bankAccountInfo?: BankAccountInfo;
}
