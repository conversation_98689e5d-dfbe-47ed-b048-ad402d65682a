import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

type Theme = 'light' | 'dark';

interface ThemeState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

// Helper to get initial theme from system preference
const getSystemTheme = (): Theme => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light'; // Default to light if matchMedia not available
};

export const useThemeStore = create<ThemeState>()(
  devtools(
    persist(
      (set) => ({
        // Initialize theme from system preference
        theme: getSystemTheme(),
        
        // Set theme explicitly
        setTheme: (theme: Theme) => set({ theme }),
        
        // Toggle between light and dark
        toggleTheme: () => set((state) => ({ 
          theme: state.theme === 'light' ? 'dark' : 'light' 
        })),
      }),
      {
        name: 'theme-storage',
      }
    )
  )
);

// Initialize theme on app load by applying the class to document
if (typeof window !== 'undefined') {
  const initializeTheme = () => {
    const { theme } = useThemeStore.getState();
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };
  
  // Run once on load
  initializeTheme();
  
  // Subscribe to theme changes
  useThemeStore.subscribe(
    (state) => {
      if (state.theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  );
}
