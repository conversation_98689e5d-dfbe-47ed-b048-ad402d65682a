import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import axios from 'axios';
import jwtDecode from 'jwt-decode';
import { 
  AuthState, 
  User, 
  OrganizerUser, 
  AdminUser, 
  LoginCredentials, 
  RegisterCustomerData, 
  RegisterOrganizerData,
  UserProfileUpdate,
  OrganizerProfileUpdate,
  AuthTokens
} from '../types/user';
import { api } from '../services/api';

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  user: null,
  error: null,
};

// Helper functions for token management
const setAuthToken = (token: string | null) => {
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('token', token);
  } else {
    delete api.defaults.headers.common['Authorization'];
    localStorage.removeItem('token');
  }
};

const getStoredToken = (): string | null => {
  return localStorage.getItem('token');
};

const isTokenValid = (token: string): boolean => {
  try {
    const decoded: any = jwtDecode(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp > currentTime;
  } catch (error) {
    return false;
  }
};

// Create the auth store
export const useAuthStore = create<
  AuthState & {
    login: (credentials: LoginCredentials) => Promise<void>;
    logout: () => Promise<void>;
    register: (data: RegisterCustomerData) => Promise<void>;
    registerCustomer: (data: RegisterCustomerData) => Promise<void>;
    registerOrganizer: (data: RegisterOrganizerData) => Promise<void>;
    checkAuth: () => Promise<void>;
    refreshToken: () => Promise<boolean>;
    updateProfile: (data: UserProfileUpdate) => Promise<void>;
    updateOrganizerProfile: (data: OrganizerProfileUpdate) => Promise<void>;
    clearError: () => void;
  }
>(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Login action
        login: async (credentials: LoginCredentials) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const response = await api.post<{
              success: boolean;
              data: { user: User | OrganizerUser | AdminUser; token: string };
              message: string;
            }>('/api/v1/auth/login', credentials);

            const { user, token } = response.data.data;
            
            // Set the token in axios headers and localStorage
            setAuthToken(token);

            set((state) => {
              state.isAuthenticated = true;
              state.user = user;
              state.isLoading = false;
            });
          } catch (error) {
            const errorMessage = 
              axios.isAxiosError(error) && error.response?.data?.error?.message
                ? error.response.data.error.message
                : 'Failed to login. Please check your credentials.';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
            });
            
            throw new Error(errorMessage);
          }
        },

        // Logout action
        logout: async () => {
          set((state) => {
            state.isLoading = true;
          });

          try {
            await api.post('/api/v1/auth/logout');
          } catch (error) {
            console.error('Logout error:', error);
          } finally {
            // Always clear the token and state even if API call fails
            setAuthToken(null);
            
            set((state) => {
              state.isAuthenticated = false;
              state.user = null;
              state.isLoading = false;
            });
          }
        },

        // Register action (alias for registerCustomer)
        register: async (data: RegisterCustomerData) => {
          return get().registerCustomer(data);
        },

        // Register customer action
        registerCustomer: async (data: RegisterCustomerData) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const response = await api.post<{
              success: boolean;
              data: { user: User; token: string };
              message: string;
            }>('/api/v1/auth/register', data);

            const { user, token } = response.data.data;
            
            // Set the token in axios headers and localStorage
            setAuthToken(token);

            set((state) => {
              state.isAuthenticated = true;
              state.user = user;
              state.isLoading = false;
            });
          } catch (error) {
            const errorMessage = 
              axios.isAxiosError(error) && error.response?.data?.error?.message
                ? error.response.data.error.message
                : 'Registration failed. Please try again.';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
            });
            
            throw new Error(errorMessage);
          }
        },

        // Register organizer action
        registerOrganizer: async (data: RegisterOrganizerData) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const response = await api.post<{
              success: boolean;
              data: { user: OrganizerUser; token: string };
              message: string;
            }>('/api/v1/auth/register/organizer', data);

            const { user, token } = response.data.data;
            
            // Set the token in axios headers and localStorage
            setAuthToken(token);

            set((state) => {
              state.isAuthenticated = true;
              state.user = user;
              state.isLoading = false;
            });
          } catch (error) {
            const errorMessage = 
              axios.isAxiosError(error) && error.response?.data?.error?.message
                ? error.response.data.error.message
                : 'Organizer registration failed. Please try again.';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
            });
            
            throw new Error(errorMessage);
          }
        },

        // Check authentication status
        checkAuth: async () => {
          set((state) => {
            state.isLoading = true;
          });

          try {
            const token = getStoredToken();
            
            if (!token || !isTokenValid(token)) {
              // Try to refresh the token if it's invalid or expired
              const refreshed = await get().refreshToken();
              
              if (!refreshed) {
                // If refresh failed, clear auth state
                setAuthToken(null);
                set((state) => {
                  state.isAuthenticated = false;
                  state.user = null;
                  state.isLoading = false;
                });
                return;
              }
            } else {
              // Set the existing token in axios headers
              setAuthToken(token);
            }

            // Fetch current user data
            const response = await api.get<{
              success: boolean;
              data: { user: User | OrganizerUser | AdminUser };
            }>('/api/v1/auth/me');

            const { user } = response.data.data;

            set((state) => {
              state.isAuthenticated = true;
              state.user = user;
              state.isLoading = false;
            });
          } catch (error) {
            console.error('Authentication check error:', error);
            
            // Clear auth state on error
            setAuthToken(null);
            set((state) => {
              state.isAuthenticated = false;
              state.user = null;
              state.isLoading = false;
              state.error = null; // Don't show error for auth check
            });
          }
        },

        // Refresh token
        refreshToken: async (): Promise<boolean> => {
          try {
            const response = await api.post<{
              success: boolean;
              data: AuthTokens;
              message: string;
            }>('/api/v1/auth/refresh-token');

            const { token } = response.data.data;
            
            if (token) {
              setAuthToken(token);
              return true;
            }
            
            return false;
          } catch (error) {
            console.error('Token refresh error:', error);
            return false;
          }
        },

        // Update user profile
        updateProfile: async (data: UserProfileUpdate) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // Handle file upload if profileImage is a File
            let updatedData = { ...data };
            
            if (data.profileImage instanceof File) {
              const formData = new FormData();
              formData.append('profileImage', data.profileImage);
              
              const uploadResponse = await api.post('/api/v1/users/upload-profile-image', formData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              });
              
              updatedData.profileImage = uploadResponse.data.data.imageUrl;
            }

            const response = await api.put<{
              success: boolean;
              data: { user: User | OrganizerUser | AdminUser };
              message: string;
            }>('/api/v1/users/profile', updatedData);

            const { user } = response.data.data;

            set((state) => {
              state.user = user;
              state.isLoading = false;
            });
          } catch (error) {
            const errorMessage = 
              axios.isAxiosError(error) && error.response?.data?.error?.message
                ? error.response.data.error.message
                : 'Failed to update profile. Please try again.';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
            });
            
            throw new Error(errorMessage);
          }
        },

        // Update organizer profile
        updateOrganizerProfile: async (data: OrganizerProfileUpdate) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // Handle file upload if companyLogo is a File
            let updatedData = { ...data };
            
            if (data.companyLogo instanceof File) {
              const formData = new FormData();
              formData.append('companyLogo', data.companyLogo);
              
              const uploadResponse = await api.post('/api/v1/organizers/upload-logo', formData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              });
              
              updatedData.companyLogo = uploadResponse.data.data.logoUrl;
            }

            const response = await api.put<{
              success: boolean;
              data: { organizerProfile: OrganizerUser['organizerProfile'] };
              message: string;
            }>('/api/v1/organizers/profile', updatedData);

            const { organizerProfile } = response.data.data;

            set((state) => {
              if (state.user && 'organizerProfile' in state.user) {
                state.user.organizerProfile = organizerProfile;
              }
              state.isLoading = false;
            });
          } catch (error) {
            const errorMessage = 
              axios.isAxiosError(error) && error.response?.data?.error?.message
                ? error.response.data.error.message
                : 'Failed to update organizer profile. Please try again.';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
            });
            
            throw new Error(errorMessage);
          }
        },

        // Clear error
        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },
      })),
      {
        name: 'auth-storage',
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
        }),
      }
    )
  )
);

// Initialize auth token from localStorage on app load
const token = getStoredToken();
if (token) {
  setAuthToken(token);
}
