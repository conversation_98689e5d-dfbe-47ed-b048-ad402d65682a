import React from 'react';

interface LoadingScreenProps {
  message?: string;
  fullScreen?: boolean;
  transparent?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  fullScreen = true,
  transparent = false,
}) => {
  const containerClasses = fullScreen
    ? 'fixed inset-0 flex items-center justify-center z-50'
    : 'flex items-center justify-center w-full h-full min-h-[200px]';
  
  const overlayClasses = transparent
    ? 'bg-opacity-50 backdrop-blur-sm'
    : 'bg-opacity-90';

  return (
    <div className={`${containerClasses} ${transparent ? '' : 'bg-white dark:bg-gray-900 ' + overlayClasses}`}>
      <div className="flex flex-col items-center">
        {/* Spinner */}
        <div className="relative">
          <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-primary-600 dark:border-primary-400 animate-spin"></div>
          <div className="absolute top-0 left-0 h-16 w-16 rounded-full border-t-4 border-b-4 border-primary-300 dark:border-primary-700 animate-ping opacity-30"></div>
        </div>
        
        {/* Loading text */}
        {message && (
          <div className="mt-4 text-gray-700 dark:text-gray-300 font-medium text-center">
            {message}
          </div>
        )}
        
        {/* TestTicket Toy logo/text */}
        <div className="mt-6 text-sm text-gray-500 dark:text-gray-400 font-medium">
          TestTicket Toy
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
