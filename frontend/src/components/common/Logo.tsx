import React from 'react';

interface LogoProps {
  className?: string;
  monochrome?: boolean;
}

const Logo: React.FC<LogoProps> = ({ className = 'h-10 w-auto', monochrome = false }) => {
  // Use currentColor to inherit text color in monochrome mode
  const primaryColor = monochrome ? 'currentColor' : '#0ea5e9'; // primary-600
  const secondaryColor = monochrome ? 'currentColor' : '#d946ef'; // secondary-600
  const accentColor = monochrome ? 'currentColor' : '#f59e0b'; // warning-500

  return (
    <svg
      className={className}
      viewBox="0 0 100 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="TestTicket Toy Logo"
    >
      {/* Ticket shape with perforated edge */}
      <path
        d="M85 5H15C12.2386 5 10 7.23858 10 10V30C10 32.7614 12.2386 35 15 35H85C87.7614 35 90 32.7614 90 30V10C90 7.23858 87.7614 5 85 5Z"
        fill="white"
        stroke={primaryColor}
        strokeWidth="2"
      />
      
      {/* Perforated edge */}
      <path
        d="M50 5V35"
        stroke={primaryColor}
        strokeWidth="2"
        strokeDasharray="4 2"
      />
      
      {/* First T */}
      <path
        d="M25 12H35M30 12V28"
        stroke={primaryColor}
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      
      {/* Second T */}
      <path
        d="M65 12H75M70 12V28"
        stroke={secondaryColor}
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      
      {/* Decorative elements */}
      <circle cx="15" cy="20" r="2" fill={accentColor} />
      <circle cx="85" cy="20" r="2" fill={accentColor} />
      
      {/* Small ticket icon */}
      <path
        d="M92 15L95 18L92 21"
        stroke={primaryColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 15L5 18L8 21"
        stroke={primaryColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Logo;
