import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { UserRole } from '../../types/user';
import LoadingScreen from '../common/LoadingScreen';

interface RoleBasedRouteProps {
  isAuthenticated: boolean;
  isLoading: boolean;
  userRole: UserRole | undefined;
  allowedRoles: UserRole[];
  redirectPath?: string;
}

/**
 * A wrapper component that protects routes requiring specific user roles
 * Redirects to unauthorized page if user doesn't have required role
 * Shows loading screen while checking authentication status
 */
const RoleBasedRoute = ({
  isAuthenticated,
  isLoading,
  userRole,
  allowedRoles,
  redirectPath = '/unauthorized'
}: RoleBasedRouteProps) => {
  const location = useLocation();

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen message="Checking authorization..." />;
  }

  // If not authenticated, redirect to login with return URL
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If authenticated but doesn't have required role, redirect to unauthorized page
  if (!userRole || !allowedRoles.includes(userRole)) {
    return <Navigate to={redirectPath} replace />;
  }

  // If authenticated and has required role, render the child routes
  return <Outlet />;
};

export default RoleBasedRoute;
