import { Navigate, Outlet, useLocation } from 'react-router-dom';
import LoadingScreen from '../common/LoadingScreen';

interface ProtectedRouteProps {
  isAuthenticated: boolean;
  isLoading: boolean;
  redirectPath?: string;
}

/**
 * A wrapper component that protects routes requiring authentication
 * Redirects to login if user is not authenticated
 * Shows loading screen while checking authentication status
 */
const ProtectedRoute = ({
  isAuthenticated,
  isLoading,
  redirectPath = '/login'
}: ProtectedRouteProps) => {
  const location = useLocation();

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen message="Checking authentication..." />;
  }

  // If not authenticated, redirect to login with return URL
  if (!isAuthenticated) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If authenticated, render the child routes
  return <Outlet />;
};

export default ProtectedRoute;
