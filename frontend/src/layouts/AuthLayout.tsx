import { Link, Outlet } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import Logo from '../components/common/Logo';

/**
 * AuthLayout component for login, registration, and other authentication pages
 * Provides a consistent layout with a split screen design
 */
const AuthLayout = () => {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      <Helmet>
        <title>Authentication - TestTicket Toy</title>
      </Helmet>

      {/* Left side - Image/Branding (hidden on mobile) */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-hero items-center justify-center p-10 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{ 
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          }} />
        </div>

        <div className="relative z-10 max-w-md text-center text-white">
          <Logo className="h-16 w-auto mx-auto mb-6" monochrome={true} />
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Welcome to TestTicket Toy</h1>
          <p className="text-xl mb-6">The comprehensive platform for event organizers and attendees</p>
          
          {/* Event ticket illustration */}
          <div className="relative mx-auto w-64 h-32 bg-white rounded-lg shadow-lg p-4 text-gray-900 transform rotate-3 mt-8">
            <div className="absolute top-0 left-1/2 h-6 w-6 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary-600"></div>
            <div className="absolute bottom-0 left-1/2 h-6 w-6 -translate-x-1/2 translate-y-1/2 rounded-full bg-primary-600"></div>
            <div className="border-dashed border-r-2 border-gray-300 absolute top-0 bottom-0 left-2/3"></div>
            <div className="flex h-full">
              <div className="w-2/3 pr-4 flex flex-col justify-between">
                <div>
                  <h3 className="font-bold text-sm">SUMMER MUSIC FESTIVAL</h3>
                  <p className="text-xs text-gray-600">Central Park, New York</p>
                </div>
                <div className="text-xs text-gray-600">June 15-18, 2025</div>
              </div>
              <div className="w-1/3 flex items-center justify-center">
                <div className="bg-gray-200 h-16 w-16 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="flex-1 flex flex-col">
        {/* Top navigation */}
        <div className="p-4 flex justify-between items-center">
          {/* Logo for mobile */}
          <Link to="/" className="md:hidden flex items-center">
            <Logo className="h-8 w-auto" />
            <span className="ml-2 text-lg font-bold text-gray-900 dark:text-white">
              TestTicket Toy
            </span>
          </Link>
          
          {/* Back to home link */}
          <Link to="/" className="text-primary-600 hover:text-primary-800 font-medium text-sm ml-auto">
            ← Back to Home
          </Link>
        </div>

        {/* Auth content */}
        <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
          <div className="w-full max-w-md space-y-8">
            {/* Outlet renders the child route components */}
            <Outlet />
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 text-center text-sm text-gray-500">
          <p>© {new Date().getFullYear()} TestTicket Toy. All rights reserved.</p>
          <div className="mt-2 space-x-4">
            <Link to="/privacy-policy" className="text-gray-500 hover:text-gray-700">
              Privacy Policy
            </Link>
            <Link to="/terms-of-service" className="text-gray-500 hover:text-gray-700">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
