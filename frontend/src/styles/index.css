/* 
 * TestTicket Toy - Main Stylesheet
 * A comprehensive ticketing platform similar to ticketpie.com
 */

/* Tailwind CSS Directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --header-height: 80px;
  --footer-height: 300px;
  --sidebar-width: 280px;
  --content-max-width: 1280px;
  
  /* Custom colors that match our Tailwind theme */
  --color-primary: #0ea5e9;
  --color-primary-dark: #0369a1;
  --color-primary-light: #7dd3fc;
  --color-secondary: #d946ef;
  --color-secondary-dark: #a21caf;
  --color-accent: #f59e0b;
  
  /* Animation durations */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
}

/* Base styles extension */
@layer base {
  html {
    scroll-behavior: smooth;
    height: 100%;
  }
  
  body {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  #root {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold;
  }
  
  h1 {
    @apply text-4xl md:text-5xl mb-6;
  }
  
  h2 {
    @apply text-3xl md:text-4xl mb-5;
  }
  
  h3 {
    @apply text-2xl md:text-3xl mb-4;
  }
  
  h4 {
    @apply text-xl md:text-2xl mb-3;
  }
  
  a {
    @apply text-primary-600 hover:text-primary-800 transition-colors duration-200;
  }
  
  /* Focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  
  /* Card components */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
  }
  
  /* Form controls */
  .form-control {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }
  
  .form-helper {
    @apply mt-1 text-sm text-gray-500;
  }
  
  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  /* Event card specific styles */
  .event-card {
    @apply card transition-all duration-300 hover:shadow-soft-lg hover:-translate-y-1;
  }
  
  .event-card-image {
    @apply aspect-w-16 aspect-h-9 overflow-hidden;
  }
  
  .event-card-image img {
    @apply object-cover w-full h-full transition-transform duration-500 hover:scale-105;
  }
  
  /* Ticket styles */
  .ticket {
    @apply relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-soft;
  }
  
  .ticket::before {
    content: '';
    @apply absolute top-0 left-1/2 h-8 w-8 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gray-100;
  }
  
  .ticket::after {
    content: '';
    @apply absolute bottom-0 left-1/2 h-8 w-8 -translate-x-1/2 translate-y-1/2 rounded-full bg-gray-100;
  }
  
  /* Organizer dashboard components */
  .dashboard-stat-card {
    @apply card p-6 flex flex-col;
  }
  
  .dashboard-stat-value {
    @apply text-3xl font-bold mt-2;
  }
  
  .dashboard-stat-label {
    @apply text-sm text-gray-500 uppercase tracking-wide;
  }
  
  /* Marketing section components */
  .hero-section {
    @apply relative overflow-hidden bg-gray-900 text-white py-16 md:py-24;
  }
  
  .feature-card {
    @apply p-6 rounded-lg bg-white dark:bg-gray-800 shadow-soft flex flex-col items-center text-center;
  }
  
  .feature-icon {
    @apply w-12 h-12 text-primary-500 mb-4;
  }
}

/* Utilities extension */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  }
  
  .bg-gradient-hero {
    background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
  }
  
  /* Truncate text with ellipsis at specific lines */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Custom animations */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }
  
  /* Print styles for tickets */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-only {
      display: block !important;
    }
    
    .ticket {
      break-inside: avoid;
      page-break-inside: avoid;
    }
  }
}
