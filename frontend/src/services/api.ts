import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';

// Get API URL from environment variables or use default
const API_URL = import.meta.env.VITE_API_URL || '/api';

// Create axios instance with default config
export const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Request interceptor for API calls
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // You can modify request config here (add headers, auth tokens, etc.)
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // You can modify successful responses here
    return response;
  },
  (error: AxiosError) => {
    const { response } = error;
    
    // Handle different error status codes
    if (response) {
      const status = response.status;
      const data = response.data as any;
      
      switch (status) {
        case 400: // Bad Request
          // Handle validation errors or bad requests
          if (data?.error?.details) {
            // If there are field-specific errors, show the first one
            const firstError = data.error.details[0];
            toast.error(firstError.message || 'Invalid request');
          } else {
            toast.error(data?.error?.message || 'Bad request');
          }
          break;
          
        case 401: // Unauthorized
          // Handle authentication errors
          toast.error('Authentication required. Please log in again.');
          
          // You can dispatch a logout action here or redirect to login
          // This will be handled by the auth store's checkAuth function
          break;
          
        case 403: // Forbidden
          toast.error('You do not have permission to perform this action');
          break;
          
        case 404: // Not Found
          toast.error('The requested resource was not found');
          break;
          
        case 409: // Conflict
          toast.error(data?.error?.message || 'A conflict occurred with your request');
          break;
          
        case 422: // Unprocessable Entity
          toast.error(data?.error?.message || 'The provided data is invalid');
          break;
          
        case 429: // Too Many Requests
          toast.error('Too many requests. Please try again later.');
          break;
          
        case 500: // Server Error
        case 502: // Bad Gateway
        case 503: // Service Unavailable
        case 504: // Gateway Timeout
          toast.error('Server error. Please try again later.');
          break;
          
        default:
          toast.error('An unexpected error occurred');
          break;
      }
    } else if (error.request) {
      // The request was made but no response was received
      toast.error('No response from server. Please check your internet connection.');
    } else {
      // Something happened in setting up the request
      toast.error('An error occurred while setting up the request');
    }

    // Pass the error down to the calling function
    return Promise.reject(error);
  }
);

/**
 * Helper function to handle file uploads
 * @param endpoint API endpoint
 * @param file File to upload
 * @param additionalData Additional form data to include
 * @returns Promise with the response
 */
export const uploadFile = async (
  endpoint: string,
  file: File,
  additionalData: Record<string, any> = {}
): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  // Add any additional data to the form
  Object.entries(additionalData).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  return api.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export default api;
