{"name": "testticket-toy-frontend", "version": "0.1.0", "private": true, "description": "Frontend for TestTicket Toy ticketing platform", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "typecheck": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@stripe/react-stripe-js": "^2.1.2", "@stripe/stripe-js": "^2.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.5.0", "chart.js": "^4.3.3", "date-fns": "^2.30.0", "formik": "^2.4.3", "framer-motion": "^10.16.0", "i18next": "^23.4.6", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^1.3.0", "react-hot-toast": "^2.4.1", "react-i18next": "^13.2.0", "react-icons": "^4.10.1", "react-markdown": "^8.0.7", "react-query": "^3.39.3", "react-router-dom": "^6.15.0", "recharts": "^2.7.3", "yup": "^1.2.0", "zustand": "^4.4.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.1", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/lodash": "^4.14.197", "@types/node": "^20.5.1", "@types/react": "^18.2.20", "@types/react-datepicker": "^4.15.0", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-react": "^4.0.4", "autoprefixer": "^10.4.15", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "msw": "^1.2.3", "postcss": "^8.4.28", "prettier": "^3.0.2", "prettier-plugin-tailwindcss": "^0.5.3", "tailwindcss": "^3.3.3", "typescript": "^5.1.6", "vite": "^4.4.9", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.2.0", "vitest": "^0.34.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}