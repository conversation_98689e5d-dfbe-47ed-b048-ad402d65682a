# TestTicket Toy

TestTicket Toy is an open-source, full-stack ticketing platform inspired by [ticketpie.com](https://www.ticketpie.com).  
It lets event organisers create, promote and analyse events while giving customers a smooth search and checkout experience.

---

## ✨ Core Features

### Organiser Portal
- **Account & Tier System** – organisers level up automatically as they sell more tickets, unlocking lower fees and extra analytics.
- **Event Builder**  
  - Multi-currency pricing  
  - Multiple ticket types (GA, VIP, Early Bird, …)  
  - Customisable seating or quantity-based tickets  
- **Promo Codes & Campaigns** – fixed or percentage discounts, usage limits, time windows.
- **Analytics Dashboard** – live sales, revenue heatmaps, traffic sources, abandoned checkouts and budget tracking.
- **Team Collaboration** – invite teammates with role-based permissions.

### Customer Experience
- **Powerful Search** – by location, date, category or keyword.
- **Secure Checkout** – supports credit/debit cards, Apple/Google Pay and local payment methods.
- **Digital Tickets** – instant email delivery and wallet-compatible passes.
- **Receipts & Order History** – downloadable invoices, easy transfers and refunds (subject to organiser rules).

### Platform & Content
- SEO-friendly marketing pages, blog, FAQ and support center built-in.
- Fully responsive design (mobile-first).
- Compliant with GDPR, PCI-DSS and strong customer authentication (SCA).

---

## 🏗️ Tech Stack

| Layer            | Technology                          |
|------------------|-------------------------------------|
| Front-end        | React + TypeScript, Vite, Tailwind  |
| Back-end API     | Node.js (Express) / NestJS          |
| Authentication   | JSON Web Tokens (JWT) + OAuth2      |
| Database         | PostgreSQL + Prisma ORM             |
| Payments         | Stripe, PayPal, plus regional PSPs  |
| Infrastructure   | Docker, Docker Compose, Nginx, AWS  |
| CI/CD            | GitHub Actions → Docker Hub → AWS   |

*(The exact stack can evolve; feel free to open an issue to discuss improvements.)*

---

## 🚀 Quick Start

### 1. Clone & install

```bash
git clone https://github.com/Chaouz/test3.git
cd test3
cp .env.example .env            # add your secrets
docker compose up --build       # starts api, db and web
```

The web app will be available at **http://localhost:3000**  
The API will be running on **http://localhost:4000**.

### 2. Seed sample data (optional)

```bash
docker compose exec api npm run seed
```

This creates a demo organiser, some events and tickets for local testing.

### 3. Run tests

```bash
docker compose exec api npm test
docker compose exec web npm test
```

### 4. Lint & format

```bash
npm run lint
npm run format
```

---

## 🛠️ Configuration

| Variable             | Description                                   |
|----------------------|-----------------------------------------------|
| `DATABASE_URL`       | Postgres connection string                    |
| `JWT_SECRET`         | Secret used to sign auth tokens               |
| `STRIPE_SECRET_KEY`  | Stripe API key for payments                   |
| `FRONTEND_URL`       | Public URL of the React app (CORS)            |
| `SMTP_*`             | Email server credentials for ticket emails    |

See `.env.example` for the full list.

---

## 🧑‍💻 Contributing

1. Fork the repo and create your feature branch: `git checkout -b feat/my-feature`
2. Commit your changes: `git commit -m "feat: awesome feature"`
3. Push to the branch: `git push origin feat/my-feature`
4. Open a Pull Request describing your changes.

All contributions (code, docs, designs, bug reports) are welcome! Check the open issues or propose new ideas.

---

## 📜 License

Distributed under the **MIT License**. See `LICENSE` for more information.

---

## 📣 Feedback & Support

Have questions or feature requests?  
Open an issue on GitHub or email **<EMAIL>**.

Happy ticketing! 🎟️
