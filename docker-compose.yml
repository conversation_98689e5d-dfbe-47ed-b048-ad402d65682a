version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: testticket-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-testticket}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-testticketpass}
      POSTGRES_DB: ${POSTGRES_DB:-testticketdb}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - testticket-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testticket"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: testticket-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - testticket-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: testticket-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DATABASE_URL: postgres://${POSTGRES_USER:-testticket}:${POSTGRES_PASSWORD:-testticketpass}@postgres:5432/${POSTGRES_DB:-testticketdb}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET:-testticket_jwt_secret_key}
      PORT: 4000
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      SMTP_FROM: ${SMTP_FROM:-"TestTicket Toy <<EMAIL>>"}
    volumes:
      - ./backend:/app
      - /app/node_modules
    ports:
      - "4000:4000"
    networks:
      - testticket-network
    command: npm run dev

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: testticket-frontend
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      VITE_API_URL: ${VITE_API_URL:-http://localhost:4000}
      VITE_STRIPE_PUBLIC_KEY: ${VITE_STRIPE_PUBLIC_KEY}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - testticket-network
    command: npm run dev

  # Adminer for database management (development only)
  adminer:
    image: adminer
    container_name: testticket-adminer
    restart: unless-stopped
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    networks:
      - testticket-network
    profiles:
      - dev
      - tools

  # Mailhog for email testing (development only)
  mailhog:
    image: mailhog/mailhog
    container_name: testticket-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - testticket-network
    profiles:
      - dev
      - tools

networks:
  testticket-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
