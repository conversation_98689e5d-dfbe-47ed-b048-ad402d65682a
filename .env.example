# TestTicket Toy - Environment Variables
# Copy this file to .env and fill in your values

# Node Environment
NODE_ENV=development

# Database Configuration
POSTGRES_USER=testticket
POSTGRES_PASSWORD=testticketpass
POSTGRES_DB=testticketdb
DATABASE_URL=**************************************************/testticketdb

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Authentication
JWT_SECRET=change_this_to_a_secure_random_string
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Server Configuration
PORT=4000
CORS_ORIGIN=http://localhost:3000

# Stripe Payment Integration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key

# Email Configuration (SMTP)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
SMTP_FROM="TestTicket Toy <<EMAIL>>"

# Frontend Configuration
VITE_API_URL=http://localhost:4000

# File Storage (for event images, tickets, etc.)
STORAGE_TYPE=local # or 's3', 'gcs', etc.
STORAGE_LOCAL_PATH=./uploads
# Uncomment if using S3
# S3_BUCKET_NAME=your-bucket-name
# S3_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Analytics (optional)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Security
COOKIE_SECRET=change_this_to_another_secure_random_string
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX=100 # 100 requests per window

# Logging
LOG_LEVEL=info # debug, info, warn, error

# Feature Flags
ENABLE_BLOG=true
ENABLE_MARKETING_PAGES=true
ENABLE_SUPPORT_CHAT=true
